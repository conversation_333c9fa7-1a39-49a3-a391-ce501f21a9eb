# Pay-Up Payment System Documentation

## Overview

Pay-Up is a versatile payment processing system that allows users to make payments via mobile money providers (M-Pesa and EcoCash). The system supports both direct payments within the application and integration with external applications through a comprehensive API.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Payment Methods](#payment-methods)
3. [Core Payment Flow](#core-payment-flow)
4. [External Application Integration](#external-application-integration)
5. [API Reference](#api-reference)
6. [Database Models](#database-models)

## System Architecture

The Pay-Up system consists of several key components:

- **Frontend UI**: Next.js-based user interfaces for payment selection and processing
- **Payment Processing API**: Backend endpoints for initiating and verifying payments
- **External Integration API**: Endpoints for third-party applications to use our payment system
- **Database**: PostgreSQL database with Prisma ORM for storing payment records and transaction details

## Payment Methods

### M-Pesa

M-Pesa is a mobile money service provided by Vodacom in Lesotho. Our integration allows users to:

- Initiate payments through the M-Pesa mobile network
- Receive real-time payment status updates
- Verify transaction completion

### EcoCash

EcoCash is another mobile money provider. Our integration supports:

- Direct EcoCash payments
- Real-time status tracking
- Transaction verification and confirmation

## Core Payment Flow

### Direct Payment Flow

1. **Payment Method Selection**: User chooses between M-Pesa and EcoCash on the payment selection page
2. **Payment Initiation**: User enters payment details (amount, phone number, etc.)
3. **Processing**: The system communicates with the respective mobile money provider
4. **Verification**: Real-time status tracking verifies the payment completion
5. **Confirmation**: User receives confirmation once the payment is successfully processed

### Payment Status Tracking

The system continuously monitors the status of initiated payments through:

- Regular polling of provider APIs
- Status updates in the UI
- Database record updates

## External Application Integration

Pay-Up provides a comprehensive API for external applications to integrate our payment services.

### Integration Flow

1. **Session Creation**: External application creates a payment session via our API
2. **User Redirection**: The user is redirected to our payment UI
3. **Payment Processing**: User selects a payment method and completes the payment
4. **Status Notification**: External application receives notifications via webhooks
5. **Verification**: External application can verify payment status via API

## API Reference

### External Payment Initiation

**Endpoint**: `POST /api/external/payment`

**Headers**:
```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

**Request Body**:
```json
{
  "amount": "100.00",
  "reference": "Order #12345",
  "redirectUrl": "https://your-app.com/payment-complete",
  "webhookUrl": "https://your-app.com/webhooks/payment",
  "metadata": {
    "orderId": "12345",
    "customerId": "user123"
  }
}
```

**Response**:
```json
{
  "success": true,
  "sessionId": "session_1625149372_a8b7c6",
  "paymentUrl": "https://pay-up.com/payment/external/session_1625149372_a8b7c6"
}
```

### Payment Status Check

**Endpoint**: `POST /api/external/payment/status`

**Headers**:
```
Authorization: Bearer YOUR_API_KEY
Content-Type: application/json
```

**Request Body**:
```json
{
  "sessionId": "session_1625149372_a8b7c6"
}
```

**Response**:
```json
{
  "success": true,
  "sessionId": "session_1625149372_a8b7c6",
  "status": "completed",
  "paymentMethod": "mpesa",
  "paymentReference": "PayRefMP189739",
  "transactionId": "000000000001",
  "completedAt": "2023-06-15T14:22:30Z"
}
```

### Webhook Notifications

When a payment is completed, the system sends a webhook notification to the provided `webhookUrl` with the following payload:

```json
{
  "sessionId": "session_1625149372_a8b7c6",
  "paymentMethod": "ecocash",
  "paymentReference": "Reference709828",
  "transactionId": "EC12345678",
  "status": "completed",
  "timestamp": "2023-06-15T14:22:30Z"
}
```

## Database Models

### MpesaPayment

Stores M-Pesa payment transactions with status tracking.

### EcocashPayment

Records EcoCash payment transactions with status tracking.

### ExternalPaymentSession

Manages payment sessions initiated by external applications, linking to the actual payment records.

## Implementation Example

### Initiating a Payment from an External Application

```javascript
// Example Node.js code to initiate a payment
async function initiatePayment(orderDetails) {
  const response = await fetch('https://pay-up.com/api/external/payment', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer $10$KLstBWXvvqvOc91kSNUSs.Y9z2q3XFSHYcvHMLWab4ArDzbXB5tl2',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      amount: orderDetails.amount,
      reference: `Order #${orderDetails.orderId}`,
      redirectUrl: 'https://your-app.com/payment-complete',
      webhookUrl: 'https://your-app.com/webhooks/payment',
      metadata: {
        orderId: orderDetails.orderId,
        customerId: orderDetails.customerId
      }
    })
  });
  
  const data = await response.json();
  
  if (data.success) {
    // Redirect user to the payment URL
    window.location.href = data.paymentUrl;
  } else {
    // Handle error
    console.error('Payment initiation failed:', data.error);
  }
}
```

### Handling Webhook Notifications

```javascript
// Example Express.js webhook handler
app.post('/webhooks/payment', express.json(), (req, res) => {
  const { 
    sessionId, 
    paymentMethod, 
    paymentReference,
    transactionId,
    status 
  } = req.body;
  
  // Update order status in your database
  if (status === 'completed') {
    // Mark order as paid
    updateOrder(sessionId, 'PAID', paymentReference, transactionId);
  } else if (status === 'failed') {
    // Handle failed payment
    updateOrder(sessionId, 'PAYMENT_FAILED');
  }
  
  // Acknowledge receipt of webhook
  res.json({ received: true });
});
```

### Checking Payment Status

```javascript
// Example code to check payment status
async function checkPaymentStatus(sessionId) {
  const response = await fetch('https://pay-up.com/api/external/payment/status', {
    method: 'POST',
    headers: {
      'Authorization': 'Bearer $10$KLstBWXvvqvOc91kSNUSs.Y9z2q3XFSHYcvHMLWab4ArDzbXB5tl2',
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ sessionId })
  });
  
  const data = await response.json();
  
  if (data.success) {
    // Update UI based on payment status
    if (data.status === 'completed') {
      showPaymentSuccess(data.paymentReference, data.transactionId);
    } else if (data.status === 'pending') {
      showPendingPayment();
    } else {
      showPaymentFailed();
    }
  } else {
    // Handle error
    console.error('Status check failed:', data.error);
  }
}
```

## Security Considerations

- All API endpoints are protected with API keys
- HTTPS is enforced for all communications
- Webhook payloads should be verified in production environments
- Sensitive payment data is never exposed to external applications

## Troubleshooting

### Common Issues

1. **Payment Not Showing as Completed**
   - Check that the mobile money provider has confirmed the transaction
   - Verify that the phone number was entered correctly
   - Ensure sufficient balance in the user's mobile money account

2. **Webhook Not Received**
   - Verify your webhook URL is publicly accessible
   - Check server logs for any errors processing the webhook
   - Verify that your server responds with a 200 status code

3. **API Authentication Failures**
   - Ensure you're using the correct API key
   - Check that the Authorization header is properly formatted

## Support

For technical support or inquiries, please contact our support <NAME_EMAIL> or open an issue on our GitHub repository.