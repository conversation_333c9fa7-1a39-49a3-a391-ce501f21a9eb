# External Payment API Integration

This document outlines how to integrate the Pay-Up external payment system into your website or web application.

## Overview

Pay-Up provides a simple API that allows any external website to accept mobile money payments through M-Pesa and EcoCash. The integration flow works as follows:

1. Your application makes a POST request to initiate a payment
2. Pay-Up returns a payment URL 
3. You redirect your user to the payment URL
4. User completes payment on Pay-Up's secure payment page
5. User is redirected back to your website with payment status

## Authentication

All API requests must include an Authorization header with a Bearer token:

```
Authorization: Bearer YOUR_API_KEY
```

Contact the Pay-Up administrator to receive your API key.

## Initiating a Payment

### Endpoint

```
POST /api/external/payment
```

### Request Body

| Field | Type | Required | Description |
|-------|------|----------|-------------|
| amount | string | Yes | Payment amount (e.g., "10.00") |
| reference | string | Yes | Your unique reference for this payment (e.g., order ID) |
| redirectUrl | string | No | URL to redirect back to after payment completion |
| webhookUrl | string | No | URL to receive payment status updates via webhook |
| domain | string | No | Your domain name (automatically detected if not provided) |
| appId | string | No | Your application ID for tracking purposes |
| userId | string | No | User ID in your system |
| metadata | object | No | Any additional data you want to associate with this payment |

### Example Request

```javascript
fetch('https://pay-up-api.example.com/api/external/payment', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: JSON.stringify({
    amount: "50.00",
    reference: "ORDER-1234",
    redirectUrl: "https://your-site.com/payment-complete",
    appId: "your-app-id",
    userId: "user-123",
    metadata: {
      productId: "prod-456",
      customerName: "John Doe"
    }
  })
})
```

### Response

```json
{
  "success": true,
  "sessionId": "ORDER-1234",
  "paymentUrl": "https://pay-up-api.example.com/payment/external/ORDER-1234"
}
```

## Handling the Redirect

After payment completion (successful or failed), the user will be redirected back to your `redirectUrl` with the following query parameters:

| Parameter | Description |
|-----------|-------------|
| sessionId | The same reference you provided when initiating the payment |
| status | Payment status: "success" or "failed" |
| reference | The payment reference number from the payment provider |
| transactionId | Transaction ID from the payment provider (only on successful payments) |
| userId | User ID you provided (if any) |
| appId | App ID you provided (if any) |

### Example Redirect URL

```
https://your-site.com/payment-complete?sessionId=ORDER-1234&status=success&reference=PAY123456&transactionId=TR789012&userId=user-123&appId=your-app-id
```

## Verifying Payment Status

If you want to verify the payment status server-side before completing the order:

### Endpoint

```
GET /api/external/session/{sessionId}
```

### Example

```javascript
fetch('https://pay-up-api.example.com/api/external/session/ORDER-1234')
  .then(response => response.json())
  .then(data => {
    if (data.success && data.session.status === 'COMPLETED') {
      // Payment is confirmed
      console.log('Payment confirmed:', data.session.completedPaymentRef);
    }
  });
```

## Webhook Notifications

If you provided a `webhookUrl`, Pay-Up will send a POST request to that URL whenever the payment status changes. The payload will include:

```json
{
  "event": "payment.completed",
  "sessionId": "ORDER-1234",
  "status": "COMPLETED",
  "reference": "PAY123456",
  "amount": "50.00",
  "transactionId": "TR789012",
  "completedAt": "2023-03-10T15:30:45Z"
}
```

## Security Considerations

1. Always verify that the `sessionId` in the redirect matches a payment you initiated
2. For critical systems, use the server-side verification endpoint to confirm payment status
3. Keep your API key secure and never expose it in client-side code
4. Consider implementing IP whitelisting for additional security

## Error Handling

The API will return appropriate HTTP status codes:

- 400: Bad Request (invalid parameters)
- 401: Unauthorized (invalid API key)
- 404: Not Found (invalid session ID)
- 500: Server Error

All error responses include a descriptive message:

```json
{
  "success": false,
  "error": "Invalid request data",
  "details": {
    "amount": ["Amount is required"]
  }
}
```

## Example Implementation

See [external-integration-example.js](./external-integration-example.js) for a complete implementation example.