// External Integration Example
// This file demonstrates how an external website would integrate with the Pay-Up system

/**
 * Example function to initiate a payment from an external website
 * @param {Object} options Payment options
 * @param {string} options.amount Amount to charge (e.g. "10.00")
 * @param {string} options.reference Reference for this payment (order ID, invoice number, etc)
 * @param {string} options.redirectUrl URL to redirect back to after payment (optional)
 * @param {string} options.userId User ID in your system (optional)
 * @param {string} options.appId Application ID (optional)
 * @returns {Promise<Object>} Response with payment URL to redirect to
 */
async function initiatePayment({ amount, reference, redirectUrl, userId, appId }) {
  try {
    // Get the current domain for tracking
    const currentDomain = window.location.origin;
    
    // Prepare payment data
    const paymentData = {
      reference,
      amount,
      domain: currentDomain,
      redirectUrl,
      appId,
      userId
    };
    
    // API endpoint for your Pay-Up instance
    const apiUrl = 'https://your-pay-up-instance.com/api/external/payment';
    
    // Your API key for authorization
    const apiKey = 'YOUR_API_KEY';
    
    // Make the API request
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      },
      body: JSON.stringify(paymentData)
    });
    
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.error || 'Failed to initiate payment');
    }
    
    return {
      success: true,
      sessionId: data.sessionId,
      paymentUrl: data.paymentUrl
    };
  } catch (error) {
    console.error('Payment initiation error:', error);
    return {
      success: false,
      error: error.message || 'Payment initiation failed'
    };
  }
}

/**
 * Redirect the user to the payment page
 */
async function handleCheckout() {
  // Example values - these would come from your checkout form
  const amount = "50.00";
  const reference = `ORDER-${Date.now()}`;
  const userId = "user_123456";
  const appId = "my-ecommerce-app";
  const redirectUrl = `${window.location.origin}/payment-complete`;
  
  const result = await initiatePayment({
    amount,
    reference,
    redirectUrl,
    userId,
    appId
  });
  
  if (result.success) {
    // Store session ID for later verification
    localStorage.setItem('pendingPaymentSession', result.sessionId);
    
    // Redirect user to payment page
    window.location.href = result.paymentUrl;
  } else {
    // Show error to user
    alert(`Payment setup failed: ${result.error}`);
  }
}

/**
 * Handle redirect back from payment page
 * This would be called on your redirectUrl page
 */
function handlePaymentReturn() {
  // Get URL parameters
  const urlParams = new URLSearchParams(window.location.search);
  const sessionId = urlParams.get('sessionId');
  const status = urlParams.get('status');
  const reference = urlParams.get('reference');
  const transactionId = urlParams.get('transactionId');
  
  // Check if this is the session we initiated
  const pendingSessionId = localStorage.getItem('pendingPaymentSession');
  
  if (status === 'success' && sessionId === pendingSessionId) {
    // Payment was successful
    // Clear the pending payment
    localStorage.removeItem('pendingPaymentSession');
    
    // Display success message
    document.getElementById('payment-result').innerHTML = `
      <div class="success">
        <h2>Payment Successful!</h2>
        <p>Reference: ${reference}</p>
        <p>Transaction ID: ${transactionId}</p>
      </div>
    `;
    
    // Update order status in your system
    updateOrderStatus(reference, 'paid');
  } else {
    // Payment failed or was cancelled
    document.getElementById('payment-result').innerHTML = `
      <div class="error">
        <h2>Payment Failed</h2>
        <p>Please try again or contact support.</p>
      </div>
    `;
  }
}

/**
 * Example implementation for updating order status in your system
 * @param {string} orderReference Order reference
 * @param {string} status New status
 */
function updateOrderStatus(orderReference, status) {
  // This would call your backend API to update the order status
  console.log(`Updating order ${orderReference} to status: ${status}`);
  
  // Example API call to your backend
  fetch('/api/orders/update-status', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      reference: orderReference,
      status
    })
  });
}

// Example HTML for checkout button
// <button onclick="handleCheckout()">Pay Now</button>

// Example HTML for payment result container on redirect page
// <div id="payment-result"></div>