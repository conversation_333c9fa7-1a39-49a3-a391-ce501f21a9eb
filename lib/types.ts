export interface PayLesothoPaymentRequest {
  amount: number;
  mobileNumber: string;
  client_reference: string;
}

export interface PayLesothoPaymentResponse {
  status_code: string;
  message?: string;
  reference?: string;
  error?: string;
}

export interface PaymentStatusResponse {
  status: 'success' | 'error' | 'pending';
  reference?: string;
  message: string;
  transaction_id?: string;
}

export interface VerifyTransactionResponse {
  status_code: string;
  message: string;
  transaction_id?: string;
  reference?: string;
  error?: string;
}

export interface EcoCashPaymentRequest {
  amount: string;
  mobileNumber: string;
  merchantid: string;
  merchantname: string;
  client_reference: string;
}

export interface EcoCashPaymentResponse {
  status_code: string;
  reference: string;
  message: string;
}

export interface EcoCashVerifyResponse {
  status_code: string;
  message: string;
}

export interface PaymentRecord {
  id?: string;
  amount: number;
  mobileNumber: string;
  clientReference: string;
  reference?: string;
  status?: 'INITIATED' | 'PENDING' | 'COMPLETED' | 'FAILED' | 'EXPIRED' | 'CANCELLED';
  statusMessage?: string;
}