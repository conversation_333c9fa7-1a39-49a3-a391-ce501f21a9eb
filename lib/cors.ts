import { NextRequest, NextResponse } from 'next/server';

/**
 * Add CORS headers to a response
 */
export function addCorsHeaders(response: NextResponse): NextResponse {
  // Define allowed origins - use environment variable or default to all
  const allowedOrigins = process.env.ALLOWED_ORIGINS ? process.env.ALLOWED_ORIGINS.split(',') : ['*'];
  
  // Get current headers
  const headers = new Headers(response.headers);
  
  // Set CORS headers
  headers.set('Access-Control-Allow-Origin', allowedOrigins.includes('*') ? '*' : allowedOrigins.join(', '));
  headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  headers.set('Access-Control-Allow-Credentials', 'true');
  
  // Create a new response with the same status, body, but with updated headers
  return new NextResponse(response.body, {
    status: response.status,
    statusText: response.statusText,
    headers
  });
}

/**
 * Wrap API handler with CORS support
 * @param handler Your API route handler
 * @returns Handler with CORS support
 */
export function withCors(handler: Function) {
  return async (req: NextRequest, context: any) => {
    // Handle preflight OPTIONS request
    if (req.method === 'OPTIONS') {
      return new NextResponse(null, {
        status: 200,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          'Access-Control-Allow-Credentials': 'true',
          'Access-Control-Max-Age': '86400', // 24 hours
        }
      });
    }

    // Call the original handler
    const response = await handler(req, context);
    
    // Add CORS headers to the response
    return addCorsHeaders(response);
  };
}