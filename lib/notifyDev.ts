type RequestDetailsType = {
    endpoint: string;
    merchantId: string;
    merchantName: string;
    amount: string | number;
    mobileNumber: string;
    client_reference: string;
    paymentType: string;
}

export const notifyDev = async(requestDetails: RequestDetailsType): Promise<void> => {

    try {
        const response = await fetch("https://localbites.co.ls/api/notifyDevTeam", {
            method: "POST",
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(requestDetails),
        });

        if (!response.ok) {
            // Handle HTTP errors
            const errorText = await response.text();
            console.error(`Failed to notify dev team. Status: ${response.status}. Response: ${errorText}`);
            // Optionally, throw to be caught by outer error boundary
            throw new Error(`Notification API error: ${response.status}`);
        }

        console.log("Dev team notified successfully", await response.json());
    } catch (error) {
        // Handle network or parsing errors
        console.error("Error notifying dev team:", error);
        // Optionally, integrate with a logging service here
    }
};
