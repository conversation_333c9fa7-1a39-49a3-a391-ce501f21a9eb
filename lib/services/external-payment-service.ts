import { prisma } from '../db/prisma';
import { ExternalPaymentStatus } from '@prisma/client';
import type { ExternalPaymentRequest } from '@/app/api/external/payment/route';

/**
 * Service to manage external payment sessions
 */
export const ExternalPaymentService = {
  /**
   * Create a new payment session
   */
  createSession: async (sessionId: string, data: ExternalPaymentRequest & { originatingDomain?: string | null }) => {
    console.log('[ExternalPaymentService] Creating session:', { sessionId, data });
    try {
      // Use a transaction to ensure atomicity
      const session = await prisma.$transaction(async (tx) => {
        // Check if session already exists
        const existing = await tx.externalPaymentSession.findUnique({
          where: { sessionId }
        });
        
        if (existing) {
          console.log('[ExternalPaymentService] Session already exists:', {
            id: existing.id,
            sessionId: existing.sessionId,
            status: existing.status
          });
          return existing;
        }
        
        // Create new session
        const newSession = await tx.externalPaymentSession.create({
          data: {
            sessionId,
            amount: data.amount,
            reference: data.reference,
            redirectUrl: data.redirectUrl || '',
            webhookUrl: data.webhookUrl,
            metadata: data.metadata as any || {},
            status: ExternalPaymentStatus.CREATED,
            appId: data.appId,
            userId: data.userId,
            originatingDomain: data.originatingDomain || null
          }
        });
        
        console.log('[ExternalPaymentService] New session created:', {
          id: newSession.id,
          sessionId: newSession.sessionId,
          status: newSession.status
        });
        
        return newSession;
      });
      
      return session;
    } catch (error) {
      console.error('[ExternalPaymentService] Error creating session:', error);
      throw error;
    }
  },
  
  /**
   * Find a session by its public session ID
   */
  findBySessionId: async (sessionId: string) => {
    console.log('[ExternalPaymentService] Finding session:', sessionId);
    try {
      const session = await prisma.externalPaymentSession.findUnique({
        where: { sessionId }
      });
      
      if (session) {
        console.log('[ExternalPaymentService] Found session:', {
          id: session.id,
          sessionId: session.sessionId,
          status: session.status
        });
      } else {
        console.log('[ExternalPaymentService] No session found for ID:', sessionId);
      }
      
      return session;
    } catch (error) {
      console.error('[ExternalPaymentService] Error finding session:', error);
      throw error;
    }
  },
  
  /**
   * Update session status
   */
  updateSessionStatus: async (sessionId: string, status: ExternalPaymentStatus) => {
    console.log('[ExternalPaymentService] Updating session status:', { sessionId, status });
    try {
      const session = await prisma.externalPaymentSession.update({
        where: { sessionId },
        data: { status }
      });
      
      console.log('[ExternalPaymentService] Session status updated:', {
        id: session.id,
        sessionId: session.sessionId,
        status: session.status
      });
      return session;
    } catch (error) {
      console.error('[ExternalPaymentService] Error updating session status:', error);
      throw error;
    }
  },
  
  /**
   * Mark session as completed with payment details
   */
  completeSession: async (
    sessionId: string, 
    paymentMethod: string, 
    paymentRef: string,
    transactionId?: string
  ) => {
    console.log('[ExternalPaymentService] Completing session:', { sessionId, paymentMethod, paymentRef, transactionId });
    try {
      const session = await prisma.externalPaymentSession.update({
        where: { sessionId },
        data: {
          status: ExternalPaymentStatus.COMPLETED,
          selectedPaymentMethod: paymentMethod,
          completedPaymentRef: paymentRef,
          transactionId,
          completedAt: new Date()
        }
      });
      
      console.log('[ExternalPaymentService] Session completed:', {
        id: session.id,
        sessionId: session.sessionId,
        status: session.status
      });
      return session;
    } catch (error) {
      console.error('[ExternalPaymentService] Error completing session:', error);
      throw error;
    }
  },
  
  /**
   * Link a payment to an external session
   */
  linkEcocashPayment: async (sessionId: string, paymentId: string) => {
    console.log('[ExternalPaymentService] Linking Ecocash payment:', { sessionId, paymentId });
    try {
      await prisma.ecocashPayment.update({
        where: { id: paymentId },
        data: {
          externalSession: {
            connect: { sessionId }
          }
        }
      });
      
      console.log('[ExternalPaymentService] Ecocash payment linked:', { sessionId, paymentId });
    } catch (error) {
      console.error('[ExternalPaymentService] Error linking Ecocash payment:', error);
      throw error;
    }
  },
  
  linkMpesaPayment: async (sessionId: string, paymentId: string) => {
    console.log('[ExternalPaymentService] Linking Mpesa payment:', { sessionId, paymentId });
    try {
      await prisma.mpesaPayment.update({
        where: { id: paymentId },
        data: {
          externalSession: {
            connect: { sessionId }
          }
        }
      });
      
      console.log('[ExternalPaymentService] Mpesa payment linked:', { sessionId, paymentId });
    } catch (error) {
      console.error('[ExternalPaymentService] Error linking Mpesa payment:', error);
      throw error;
    }
  },
  
  /**
   * Find payments by reference
   */
  findEcocashPaymentByReference: async (reference: string) => {
    console.log('[ExternalPaymentService] Finding Ecocash payment by reference:', reference);
    try {
      const payment = await prisma.ecocashPayment.findUnique({
        where: { reference }
      });
      
      if (payment) {
        console.log('[ExternalPaymentService] Found Ecocash payment:', {
          id: payment.id,
          reference: payment.reference,
          status: payment.status
        });
      } else {
        console.log('[ExternalPaymentService] No Ecocash payment found for reference:', reference);
      }
      
      return payment;
    } catch (error) {
      console.error('[ExternalPaymentService] Error finding Ecocash payment:', error);
      throw error;
    }
  },
  
  findMpesaPaymentByReference: async (reference: string) => {
    console.log('[ExternalPaymentService] Finding Mpesa payment by reference:', reference);
    try {
      const payment = await prisma.mpesaPayment.findUnique({
        where: { reference }
      });
      
      if (payment) {
        console.log('[ExternalPaymentService] Found Mpesa payment:', {
          id: payment.id,
          reference: payment.reference,
          status: payment.status
        });
      } else {
        console.log('[ExternalPaymentService] No Mpesa payment found for reference:', reference);
      }
      
      return payment;
    } catch (error) {
      console.error('[ExternalPaymentService] Error finding Mpesa payment:', error);
      throw error;
    }
  },
  
  /**
   * Record webhook notification attempt
   */
  recordWebhookNotification: async (sessionId: string, response: any) => {
    console.log('[ExternalPaymentService] Recording webhook notification:', { sessionId, response });
    try {
      const session = await prisma.externalPaymentSession.update({
        where: { sessionId },
        data: {
          webhookResponse: response,
          webhookSentAt: new Date()
        }
      });
      
      console.log('[ExternalPaymentService] Webhook notification recorded:', {
        id: session.id,
        sessionId: session.sessionId,
        webhookResponse: session.webhookResponse
      });
      return session;
    } catch (error) {
      console.error('[ExternalPaymentService] Error recording webhook notification:', error);
      throw error;
    }
  }
};

export default ExternalPaymentService;