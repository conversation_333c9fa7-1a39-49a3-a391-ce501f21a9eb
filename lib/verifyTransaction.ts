import { <PERSON><PERSON>, MPESA_MERCHANT_ID, PAY_LESOTHO_BASE_URL } from "./constants";
import { VerifyTransactionResponse } from "./types";

/**
 * Verifies a transaction using the reference code
 * @param reference The reference code received from the payment initiation
 * @returns VerifyTransactionResponse object with transaction details
 */
export async function verifyTransaction(
  reference: string
): Promise<VerifyTransactionResponse> {
  const baseUrl = process.env.NEXT_PUBLIC_PAY_LESOTHO_BASE_URL || PAY_LESOTHO_BASE_URL;
  const merchantId = process.env.PAY_LESOTHO_MERCHANT_ID || MPESA_MERCHANT_ID;
  const bearerToken = Bearer;

  if (!baseUrl || !merchantId || !bearerToken) {
    throw new Error('Pay Lesotho configuration is missing');
  }

  try {
    const response = await fetch(`${baseUrl}/api/v1/vcl/verify/${reference}/62915`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${bearerToken}`
      }
    });

    const contentType = response.headers.get("content-type") || "";
    if (!contentType.includes("application/json")) {
      const text = await response.text();
      console.error("Expected JSON response but received:", text);
      throw new Error("Invalid JSON response");
    }

    const data = await response.json();
    
    // Normalize the response format
    return {
      status_code: data.status_code || data.status || 'ERROR',
      message: data.message || 'Transaction verification completed',
      transaction_id: data.transaction_id || data.transactionId,
      reference: reference,
      error: !response.ok ? (data.error || response.statusText) : undefined,
    };
  } catch (error) {
    console.error('Transaction verification failed:', error);
    return {
      status_code: 'ERROR',
      message: 'Transaction verification failed',
      reference: reference,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}