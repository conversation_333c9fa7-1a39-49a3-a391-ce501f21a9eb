import { Bear<PERSON>, MPESA_MERCHANT_ID, PAY_LESOTHO_BASE_URL } from "./constants";
import { PaymentStatusResponse } from "./types";
import { verifyTransaction } from "./verifyTransaction";

/**
 * Checks the status of a payment using the reference code
 * Note: This is a placeholder function. The actual implementation would depend on
 * Pay Lesotho's API for checking payment status.
 */
export async function checkPaymentStatus(
    reference: string
  ): Promise<PaymentStatusResponse> {
    const baseUrl = PAY_LESOTHO_BASE_URL;
    const merchantId =  MPESA_MERCHANT_ID;
    const bearerToken = Bearer;
  
    if (!baseUrl || !merchantId || !bearerToken) {
      throw new Error('Pay Lesotho configuration is missing');
    }
  
    try {
      // Use the verify transaction endpoint to check payment status
      const verifyResponse = await verifyTransaction(reference);
      
      if (verifyResponse.status_code === 'INS-0') {
        return {
          status: 'success',
          reference: verifyResponse.reference,
          message: verifyResponse.message,
          transaction_id: verifyResponse.transaction_id
        };
      }
      
      // Fallback to the status endpoint if verify doesn't work
      const response = await fetch(`${baseUrl}/api/v1/vcl/status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${bearerToken}`
        },
        body: JSON.stringify({
          merchantid: merchantId,
          reference: reference,
        }),
      });
  
      if (!response.ok) {
        return {
          status: 'error',
          message: 'Failed to check payment status',
        };
      }
  
      const data = await response.json();
      
      // Adapt this based on the actual response format from Pay Lesotho
      return {
        status: data.status_code === 'INS-0' ? 'success' : 'pending',
        reference: data.reference,
        message: data.message,
      };
    } catch (error) {
      console.error('Payment status check failed:', error);
      return {
        status: 'error',
        message: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }