import { EcoCashPaymentRequest } from '@/lib/types';
import { EcocashPaymentStatus } from '@prisma/client';
import { prisma } from './prisma';

/**
 * Creates a new Ecocash payment record in the database
 */
export async function createEcocashPayment(
  paymentRequest: EcoCashPaymentRequest,
  reference?: string,
  statusCode?: string,
  statusMessage?: string
) {
  try {
    // Make sure we're using the enum type correctly
    const status = reference ? EcocashPaymentStatus.PENDING : EcocashPaymentStatus.INITIATED;
    
    const payment = await prisma.ecocashPayment.create({
      data: {
        amount: paymentRequest.amount,
        mobileNumber: paymentRequest.mobileNumber,
        clientReference: paymentRequest.client_reference,
        reference: reference,
        status: status,
        statusCode: statusCode,
        statusMessage: statusMessage,
      },
    });

    console.log(`Ecocash payment recorded in database with id: ${payment.id}`);
    return payment;
  } catch (error) {
    console.error('Error creating Ecocash payment record:', error);
    throw error;
  }
}

/**
 * Logs a callback response from Ecocash
 */
export async function logEcocashCallback(
  paymentId: string,
  statusCode: string,
  message: string,
  rawPayload: any
) {
  try {
    const callback = await prisma.ecocashCallback.create({
      data: {
        paymentId,
        statusCode,
        message,
        rawPayload,
      },
    });
    
    console.log(`Ecocash callback logged with id: ${callback.id}`);
    return callback;
  } catch (error) {
    console.error('Error logging Ecocash callback:', error);
    throw error;
  }
}

/**
 * Find an Ecocash payment by reference
 */
export async function findEcocashPaymentByReference(reference: string) {
  return prisma.ecocashPayment.findUnique({
    where: { reference },
  });
}

/**
 * Updates the status of an Ecocash payment after verification
 */
export async function updateEcocashPaymentVerification(
  paymentId: string,
  status: EcocashPaymentStatus,
  statusCode: string,
  statusMessage: string
) {
  try {
    // Make sure the status is a valid enum value
    const payment = await prisma.ecocashPayment.update({
      where: { id: paymentId },
      data: {
        status,
        statusCode,
        statusMessage,
        verificationDate: new Date(),
      },
    });

    console.log(`Ecocash payment ${paymentId} updated with status: ${status}`);
    return payment;
  } catch (error) {
    console.error('Error updating Ecocash payment verification:', error);
    throw error;
  }
}
