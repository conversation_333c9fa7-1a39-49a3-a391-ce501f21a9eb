import { EcoCashPaymentRequest, EcoCashPaymentResponse, EcoCashVerifyResponse } from './types';
import { ECOCASH_API_URL, ECOCASH_MERCHANT_ID, ECOCASH_MERCHANT_NAME, Bearer } from './constants';
import { notifyDev } from './notifyDev';

export async function initiateEcoCashPayment(
  paymentData: EcoCashPaymentRequest
): Promise<EcoCashPaymentResponse> {
  console.log('Initiating EcoCash payment with data:', {
    ...paymentData,
    mobileNumber: paymentData.mobileNumber.substring(0, 4) + '****' // Mask phone number for logging
  });

  const endpoint = `${ECOCASH_API_URL}/payment`;

  // Prepare the raw request details for notifyDev
  const requestDetails = {
    endpoint,
    merchantId: ECOCASH_MERCHANT_ID,
    merchantName: ECOCASH_MERCHANT_NAME,
    amount: paymentData.amount,
    mobileNumber: paymentData.mobileNumber,
    client_reference: paymentData.client_reference,
    paymentType: "ECOCASH",
  };

  // Build form data
  const formData = new FormData();
  formData.append('merchantid', ECOCASH_MERCHANT_ID);
  formData.append('merchantname', ECOCASH_MERCHANT_NAME);
  formData.append('amount', paymentData.amount);
  formData.append('mobileNumber', paymentData.mobileNumber);
  formData.append('client_reference', paymentData.client_reference);

  // Set up abort controller for the 60s timeout
  const controller = new AbortController();

  // After 30s, notify dev if still pending
  const notifyTimeout = setTimeout(() => {
    notifyDev(requestDetails).catch(err =>
      console.error('Error in notifyDev:', err)
    );
  }, 30_000);

  // After 60s, abort the fetch
  const abortTimeout = setTimeout(() => {
    controller.abort();
  }, 60_000);

  try {
    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer Bearer ${Bearer}`,
      },
      body: formData,
      signal: controller.signal,
    });

    // Clean up timers
    clearTimeout(notifyTimeout);
    clearTimeout(abortTimeout);

    console.log('EcoCash API response status:', response.status);
    const data = await response.json();
    console.log('EcoCash API response data:', data);

    return data;
  } catch (error: any) {
    // Clean up timers
    clearTimeout(notifyTimeout);
    clearTimeout(abortTimeout);

    if (error.name === 'AbortError') {
      console.error('EcoCash payment initiation timed out');
      throw new Error('Transaction timed out');
    } else {
      console.error('EcoCash payment initiation error:', error);
      throw new Error('Failed to initiate EcoCash payment');
    }
  }
}


export async function verifyEcoCashTransaction(
  reference: string
): Promise<EcoCashVerifyResponse> {
  console.log('Verifying EcoCash transaction:', reference);

  try {
    const response = await fetch(`${ECOCASH_API_URL}/verify/${reference}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer Bearer ${Bearer}`,  // Fixed format to match working curl example
      },
    });

    console.log('EcoCash verify API response status:', response.status);
    const data = await response.json();
    console.log('EcoCash verify API response data:', data);
    
    return data;
  } catch (error) {
    console.error('EcoCash transaction verification error:', error);
    throw new Error('Failed to verify EcoCash transaction');
  }
}