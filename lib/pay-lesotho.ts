import { <PERSON><PERSON>, MPESA_MERCHANT_ID, MPESA_MERCHANT_NAME, PAY_LESOTHO_BASE_URL } from "./constants";
import { notifyDev } from "./notifyDev";
import { PayLesothoPaymentRequest, PayLesothoPaymentResponse } from "./types";

/**
 * Initiates a payment request to Pay Lesotho with request timeouts and dev notifications
 * The response will be stored in the database by the calling function
 */
export async function initiatePayment(
  paymentData: PayLesothoPaymentRequest
): Promise<PayLesothoPaymentResponse> {
  console.log('Initiating payment with data:', JSON.stringify(paymentData, null, 2));
  
  const baseUrl = process.env.NEXT_PUBLIC_PAY_LESOTHO_BASE_URL || PAY_LESOTHO_BASE_URL;
  const merchantId = process.env.PAY_LESOTHO_MERCHANT_ID || MPESA_MERCHANT_ID;
  const merchantName = process.env.NEXT_PUBLIC_PAY_LESOTHO_MERCHANT_NAME || MPESA_MERCHANT_NAME;
  const bearerToken = Bearer;

  console.log('Configuration:', { baseUrl, merchantId, merchantName, hasToken: !!bearerToken });

  if (!baseUrl || !merchantId || !merchantName || !bearerToken) {
    console.error('Pay Lesotho configuration is missing');
    throw new Error('Pay Lesotho configuration is missing');
  }

  const endpoint = `${baseUrl}/api/v1/vcl/payment`;

  // Prepare request details for notifyDev
  const requestDetails = {
    endpoint,
    merchantId,
    merchantName,
    amount: paymentData.amount,
    mobileNumber: paymentData.mobileNumber,
    client_reference: paymentData.client_reference,
    paymentType: "MPESA",
  };

  // Build form data
  const formData = new FormData();
  formData.append('merchantid', merchantId);
  formData.append('merchantname', merchantName);
  formData.append('amount', paymentData.amount.toString());
  formData.append('mobileNumber', paymentData.mobileNumber);
  formData.append('client_reference', paymentData.client_reference);

  // Setup AbortController for 60s timeout
  const controller = new AbortController();

  // After 30s notify dev if still pending
  const notifyTimeout = setTimeout(() => {
    notifyDev(requestDetails).catch(err =>
      console.error('Error in notifyDev:', err)
    );
  }, 30_000);

  // After 60s abort the request
  const abortTimeout = setTimeout(() => {
    controller.abort();
  }, 60_000);

  try {
    console.log(`Making request to ${endpoint} with form data`, requestDetails);
    console.log('Authorization header:', `Bearer ${bearerToken}`);

    const response = await fetch(endpoint, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${bearerToken}`
      },
      body: formData,
      signal: controller.signal,
    });

    // Clean up timers
    clearTimeout(notifyTimeout);
    clearTimeout(abortTimeout);

    console.log('Response status:', response.status);
    const contentType = response.headers.get('content-type') || '';
    console.log('Content-Type:', contentType);

    if (contentType.includes('application/json')) {
      const jsonResponse = await response.json();
      console.log('JSON response:', JSON.stringify(jsonResponse, null, 2));
      return jsonResponse;
    } else {
      const text = await response.text();
      console.error('Expected JSON response but received:', text);
      throw new Error('Invalid JSON response');
    }
  } catch (error: any) {
    // Clean up timers
    clearTimeout(notifyTimeout);
    clearTimeout(abortTimeout);

    if (error.name === 'AbortError') {
      console.error('Payment transaction timed out');
      throw new Error('Payment transaction timed out');
    } else {
      console.error('Payment request failed:', error instanceof Error ? error.message : error);
      console.error('Error stack:', error instanceof Error ? error.stack : 'No stack available');
      return {
        status_code: 'ERROR',
        message: 'Payment request failed',
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
}
