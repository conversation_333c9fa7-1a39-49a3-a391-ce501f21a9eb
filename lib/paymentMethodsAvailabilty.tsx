"use server";

import { prisma } from "./db";

export const checkPaymentMethodAvailability = async (paymentMethod: string) => {
  try {
    console.log("paymentMethod: ", paymentMethod)
    const response = await prisma.paymentMethodAvailability.findMany({
      where: { paymentMethod },
    });
    console.log("primsa reponse: ", response);
    if (!response) {
      return { available: true, message: "Payment method not found" };
    }
    return response;
  } catch (error) {
    console.error("Error checking payment method availability:", error);
    return { available: false, message: "Failed to check availability" };
  }
};

export const upsertPaymentMethodAvailability = async (
  paymentMethod: string,
  isActive: boolean,
  id?: string
) => {
  try {
    let response;

    if (id) {
      // If ID is provided, try to update the existing record
      response = await prisma.paymentMethodAvailability.upsert({
        where: { id },
        update: { isActive },
        create: { paymentMethod, isActive },
      });
    } else {
      // If no ID is provided, find by payment method or create new
      const existingMethod = await prisma.paymentMethodAvailability.findFirst({
        where: { paymentMethod },
      });

      if (existingMethod) {
        // Update existing record if found
        response = await prisma.paymentMethodAvailability.update({
          where: { id: existingMethod.id },
          data: { isActive },
        });
      } else {
        // Create new record if not found
        response = await prisma.paymentMethodAvailability.create({
          data: { paymentMethod, isActive },
        });
      }
    }

    return response;
  } catch (error) {
    console.error("Error upserting payment method availability:", error);
    return { error: "Failed to upsert availability" };
  }
};
