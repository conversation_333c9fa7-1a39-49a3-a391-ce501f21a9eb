// Constants for Mpesa Lesotho API
export const Bearer: string = "$10$KLstBWXvvqvOc91kSNUSs.Y9z2q3XFSHYcvHMLWab4ArDzbXB5tl2";
export const paymentMethodsAvailabiltyBearerToken: string = "2a20h8wePZ8PFBhWqrdmbGuoenef7CleEA7QjgHvJ9XcWDIHkajlGYQ6"
export const PAY_LESOTHO_BASE_URL: string = 'https://api.paylesotho.co.ls';
export const MPESA_MERCHANT_ID: string = "118920";
// export const MPESA_MERCHANT_ID: string = "51751";
export const MPESA_MERCHANT_NAME: string = "NEXT GEN. TECHNOLOGIES";
// export const MPESA_MERCHANT_NAME: string = "<PERSON>'s Consultancy";

// Constants for EcoCash API
export const ECOCASH_API_URL: string = 'https://api.paylesotho.co.ls/api/v1/econet';
// export const ECOCASH_MERCHANT_ID: string = '062739148';
export const ECOCASH_MERCHANT_ID: string = '62697550';
export const ECOCASH_MERCHANT_NAME: string = 'NEXT GEN TECHNOLOGIES';
// export const ECOCASH_MERCHANT_NAME: string = "Suzanne's Consultancy";
