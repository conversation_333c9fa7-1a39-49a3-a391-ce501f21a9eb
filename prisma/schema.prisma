// M-Pesa Payment Tracking Schema

datasource db {
  provider = "postgresql" // You can change this to your preferred database
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

// Main payment transaction model
model MpesaPayment {
  id              String   @id @default(uuid())
  amount          Decimal  @db.Decimal(10, 2)
  mobileNumber    String
  clientReference String   // purpose of payment - "purchase of goods and services"
  
  // Response after payment initiation
  reference       String?  @unique // "PayRefMP189739" - unique identifier from M-Pesa
  initiationDate  DateTime @default(now())
  
  // Fields for verification status
  status          PaymentStatus @default(INITIATED)
  transactionId   String?       // "000000000001" - received after verification
  verificationReference String? // "TQuerRefMP189053" - reference received during verification
  verificationDate     DateTime?
  statusMessage   String?       // Store the message from M-Pesa response
  
  // Audit fields
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  paymentCallbacks PaymentCallback[]
  externalSession  ExternalPaymentSession? @relation(fields: [externalSessionId], references: [id])
  externalSessionId String?

  @@index([reference])
  @@index([transactionId])
  @@index([mobileNumber])
  @@index([status])
}

// Store all callback responses/events from M-Pesa
model PaymentCallback {
  id          String   @id @default(uuid())
  paymentId   String
  payment     MpesaPayment @relation(fields: [paymentId], references: [id])
  statusCode  String
  message     String
  rawPayload  Json     // Store the full callback payload for reference
  createdAt   DateTime @default(now())

  @@index([paymentId])
}

// Enum to track payment status throughout the flow
enum PaymentStatus {
  INITIATED      // Payment request sent to M-Pesa
  PENDING        // Payment initiated but awaiting customer action
  COMPLETED      // Payment successfully completed and verified
  FAILED         // Payment attempt failed
  EXPIRED        // Payment request expired without completion
  CANCELLED      // Payment was cancelled by user or merchant
}

// Ecocash payment transaction model
model EcocashPayment {
  id              String   @id @default(uuid())
  amount          Decimal  @db.Decimal(10, 2)
  mobileNumber    String
  clientReference String   // purpose of payment
  
  // Response after payment initiation
  reference       String?  @unique // "Reference709828" - unique identifier from Ecocash
  initiationDate  DateTime @default(now())
  
  // Fields for verification status
  status          EcocashPaymentStatus @default(INITIATED)
  statusCode      String?       // "200", "415", "401" etc.
  verificationDate DateTime?
  statusMessage   String?       // Store the message from Ecocash response
  
  // Audit fields
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  ecocashCallbacks EcocashCallback[]
  externalSession  ExternalPaymentSession? @relation(fields: [externalSessionId], references: [id])
  externalSessionId String?

  @@index([reference])
  @@index([mobileNumber])
  @@index([status])
}

// Store all callback responses/events from Ecocash
model EcocashCallback {
  id          String   @id @default(uuid())
  paymentId   String
  payment     EcocashPayment @relation(fields: [paymentId], references: [id])
  statusCode  String
  message     String
  rawPayload  Json     // Store the full callback payload for reference
  createdAt   DateTime @default(now())

  @@index([paymentId])
}

// Enum to track Ecocash payment status throughout the flow
enum EcocashPaymentStatus {
  INITIATED      // Payment request sent to Ecocash
  PENDING        // Payment initiated but awaiting PIN confirmation
  COMPLETED      // Payment successfully completed and verified
  FAILED         // Payment attempt failed (insufficient balance, wrong number, etc)
  EXPIRED        // Payment request expired without completion
  CANCELLED      // Payment was cancelled
}

// External Payment Session for integration with other applications
model ExternalPaymentSession {
  id              String   @id @default(uuid())
  sessionId       String   @unique   // The public session ID provided to external apps
  amount          String
  reference       String
  redirectUrl     String
  webhookUrl      String?
  metadata        Json?    // Additional metadata provided by the external app
  status          ExternalPaymentStatus @default(CREATED)
  userId          String?   // ID of the user in the external application
  appId           String?   // ID of the application initiating the payment
  originatingDomain String?
  
  // The payment method selected by the user (if any)
  selectedPaymentMethod String?
  
  // Payment references after completion
  completedPaymentRef   String?
  transactionId         String?
  
  // Timestamps
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  completedAt     DateTime?
  
  // Relations - the actual payment records
  mpesaPayments   MpesaPayment[]
  ecocashPayments EcocashPayment[]
  
  // Response sent back to the external application
  webhookResponse Json?
  webhookSentAt   DateTime?

  @@index([sessionId])
  @@index([status])
  @@index([createdAt])
  @@index([reference])
  @@index([userId])  // Optional: Index if you query by userId
  @@index([appId])
}

model paymentMethodAvailability {
  id              String   @id @default(uuid())
  paymentMethod   String
  isActive        Boolean  @default(true)
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt
  
  // Add relation field to connect to unavailability dates
  unavailabilityDates datesOfPaymentMethodUnavailability[]

  @@index([paymentMethod])
}

model datesOfPaymentMethodUnavailability {
  id              String   @id @default(uuid())
  paymentMethod   String
  startDate       DateTime
  endDate         DateTime?
  reason          String?
  paymentMethodId String
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Add relation to connect to payment method availability
  availability    paymentMethodAvailability @relation(fields: [paymentMethodId], references: [id])
  
  @@index([paymentMethod])
  @@index([paymentMethodId])
}

// External payment session status
enum ExternalPaymentStatus {
  CREATED        // Session created but payment not yet started
  PROCESSING     // User is in the payment flow
  COMPLETED      // Payment was successfully completed
  FAILED         // Payment attempt failed
  CANCELLED      // User cancelled the payment
  EXPIRED        // Session expired without completion
}
