-- CreateTable
CREATE TABLE "paymentMethodAvailability" (
    "id" TEXT NOT NULL,
    "paymentMethod" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "reason" TEXT,
    "isActive" BOOLEAN NOT NULL DEFAULT true,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "paymentMethodAvailability_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "paymentMethodAvailability_paymentMethod_idx" ON "paymentMethodAvailability"("paymentMethod");
