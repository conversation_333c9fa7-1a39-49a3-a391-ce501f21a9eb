/*
  Warnings:

  - You are about to drop the column `endDate` on the `paymentMethodAvailability` table. All the data in the column will be lost.
  - You are about to drop the column `reason` on the `paymentMethodAvailability` table. All the data in the column will be lost.
  - You are about to drop the column `startDate` on the `paymentMethodAvailability` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE "paymentMethodAvailability" DROP COLUMN "endDate",
DROP COLUMN "reason",
DROP COLUMN "startDate";

-- CreateTable
CREATE TABLE "datesOfPaymentMethodUnavailability" (
    "id" TEXT NOT NULL,
    "paymentMethod" TEXT NOT NULL,
    "startDate" TIMESTAMP(3) NOT NULL,
    "endDate" TIMESTAMP(3),
    "reason" TEXT,
    "paymentMethodId" TEXT NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "datesOfPaymentMethodUnavailability_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "datesOfPaymentMethodUnavailability_paymentMethod_idx" ON "datesOfPaymentMethodUnavailability"("paymentMethod");

-- CreateIndex
CREATE INDEX "datesOfPaymentMethodUnavailability_paymentMethodId_idx" ON "datesOfPaymentMethodUnavailability"("paymentMethodId");

-- AddForeignKey
ALTER TABLE "datesOfPaymentMethodUnavailability" ADD CONSTRAINT "datesOfPaymentMethodUnavailability_paymentMethodId_fkey" FOREIGN KEY ("paymentMethodId") REFERENCES "paymentMethodAvailability"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
