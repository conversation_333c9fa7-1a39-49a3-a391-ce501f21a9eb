-- CreateEnum
CREATE TYPE "PaymentStatus" AS ENUM ('INITIATED', 'PENDING', 'COMPLETED', 'FAILED', 'EXPIRED', 'CANCELLED');

-- CreateTable
CREATE TABLE "MpesaPayment" (
    "id" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "mobileNumber" TEXT NOT NULL,
    "clientReference" TEXT NOT NULL,
    "reference" TEXT,
    "initiationDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "PaymentStatus" NOT NULL DEFAULT 'INITIATED',
    "transactionId" TEXT,
    "verificationReference" TEXT,
    "verificationDate" TIMESTAMP(3),
    "statusMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "MpesaPayment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "PaymentCallback" (
    "id" TEXT NOT NULL,
    "paymentId" TEXT NOT NULL,
    "statusCode" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "rawPayload" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "PaymentCallback_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "MpesaPayment_reference_key" ON "MpesaPayment"("reference");

-- CreateIndex
CREATE INDEX "MpesaPayment_reference_idx" ON "MpesaPayment"("reference");

-- CreateIndex
CREATE INDEX "MpesaPayment_transactionId_idx" ON "MpesaPayment"("transactionId");

-- CreateIndex
CREATE INDEX "MpesaPayment_mobileNumber_idx" ON "MpesaPayment"("mobileNumber");

-- CreateIndex
CREATE INDEX "MpesaPayment_status_idx" ON "MpesaPayment"("status");

-- CreateIndex
CREATE INDEX "PaymentCallback_paymentId_idx" ON "PaymentCallback"("paymentId");

-- AddForeignKey
ALTER TABLE "PaymentCallback" ADD CONSTRAINT "PaymentCallback_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "MpesaPayment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
