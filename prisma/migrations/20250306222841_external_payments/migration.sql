-- CreateEnum
CREATE TYPE "ExternalPaymentStatus" AS ENUM ('CREATED', 'PROCESSING', 'COMPLETED', 'FAILED', 'CANCELLED', 'EXPIRED');

-- AlterTable
ALTER TABLE "EcocashPayment" ADD COLUMN     "externalSessionId" TEXT;

-- AlterTable
ALTER TABLE "MpesaPayment" ADD COLUMN     "externalSessionId" TEXT;

-- CreateTable
CREATE TABLE "ExternalPaymentSession" (
    "id" TEXT NOT NULL,
    "sessionId" TEXT NOT NULL,
    "amount" TEXT NOT NULL,
    "reference" TEXT NOT NULL,
    "redirectUrl" TEXT NOT NULL,
    "webhookUrl" TEXT,
    "metadata" JSONB,
    "status" "ExternalPaymentStatus" NOT NULL DEFAULT 'CREATED',
    "selectedPaymentMethod" TEXT,
    "completedPaymentRef" TEXT,
    "transactionId" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,
    "completedAt" TIMESTAMP(3),
    "webhookResponse" JSONB,
    "webhookSentAt" TIMESTAMP(3),

    CONSTRAINT "ExternalPaymentSession_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "ExternalPaymentSession_sessionId_key" ON "ExternalPaymentSession"("sessionId");

-- CreateIndex
CREATE INDEX "ExternalPaymentSession_sessionId_idx" ON "ExternalPaymentSession"("sessionId");

-- CreateIndex
CREATE INDEX "ExternalPaymentSession_status_idx" ON "ExternalPaymentSession"("status");

-- AddForeignKey
ALTER TABLE "MpesaPayment" ADD CONSTRAINT "MpesaPayment_externalSessionId_fkey" FOREIGN KEY ("externalSessionId") REFERENCES "ExternalPaymentSession"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "EcocashPayment" ADD CONSTRAINT "EcocashPayment_externalSessionId_fkey" FOREIGN KEY ("externalSessionId") REFERENCES "ExternalPaymentSession"("id") ON DELETE SET NULL ON UPDATE CASCADE;
