-- CreateEnum
CREATE TYPE "EcocashPaymentStatus" AS ENUM ('INITIATED', 'PENDING', 'COMPLETED', 'FAILED', 'EXPIRED', 'CANCELLED');

-- CreateTable
CREATE TABLE "EcocashPayment" (
    "id" TEXT NOT NULL,
    "amount" DECIMAL(10,2) NOT NULL,
    "mobileNumber" TEXT NOT NULL,
    "clientReference" TEXT NOT NULL,
    "reference" TEXT,
    "initiationDate" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "status" "EcocashPaymentStatus" NOT NULL DEFAULT 'INITIATED',
    "statusCode" TEXT,
    "verificationDate" TIMESTAMP(3),
    "statusMessage" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "EcocashPayment_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "EcocashCallback" (
    "id" TEXT NOT NULL,
    "paymentId" TEXT NOT NULL,
    "statusCode" TEXT NOT NULL,
    "message" TEXT NOT NULL,
    "rawPayload" JSONB NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "EcocashCallback_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "EcocashPayment_reference_key" ON "EcocashPayment"("reference");

-- CreateIndex
CREATE INDEX "EcocashPayment_reference_idx" ON "EcocashPayment"("reference");

-- CreateIndex
CREATE INDEX "EcocashPayment_mobileNumber_idx" ON "EcocashPayment"("mobileNumber");

-- CreateIndex
CREATE INDEX "EcocashPayment_status_idx" ON "EcocashPayment"("status");

-- CreateIndex
CREATE INDEX "EcocashCallback_paymentId_idx" ON "EcocashCallback"("paymentId");

-- AddForeignKey
ALTER TABLE "EcocashCallback" ADD CONSTRAINT "EcocashCallback_paymentId_fkey" FOREIGN KEY ("paymentId") REFERENCES "EcocashPayment"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
