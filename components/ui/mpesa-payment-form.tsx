"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Loader2 } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { PayLesothoPaymentResponse } from "@/lib/types";

const formSchema = z.object({
  amount: z.string().min(1, { message: "Amount is required" }),
  mobileNumber: z
    .string()
    .min(8, { message: "Valid mobile number is required" }),
  client_reference: z.string().min(1, { message: "Reference is required" }),
});

interface MPesaPaymentFormProps {
  onPaymentComplete: (response: PayLesothoPaymentResponse) => void;
  defaultAmount?: string;
  defaultReference?: string;
}

export function MPesaPaymentForm({
  onPaymentComplete,
  defaultAmount = "",
  defaultReference = "",
}: MPesaPaymentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: defaultAmount,
      mobileNumber: "",
      client_reference: defaultReference,
    },
  });

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true);
    setError(null);

    try {
      const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || ""; // Default to empty string to avoid undefined
      const response = await fetch(`${baseUrl}/api/payments`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(values),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || data.error || "Payment failed");
      }

      onPaymentComplete(data);
    } catch (error) {
      console.error("Payment submission error:", error);
      console.error(
        "message:",
        error instanceof Error ? error.message : "Failed to process payment"
      );
      setError(
        error instanceof Error
          ? error.message
          : "Payment system unavailable. Please try again later or contact support if the problem persists."
      );
    } finally {
      setIsSubmitting(false);
    }
  }

  return (
    <Card className="shadow-2xl border-none">
      <CardHeader>
        <CardTitle>M-Pesa Payment</CardTitle>
        <CardDescription>
          Enter your details to make a payment via M-Pesa
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Payment Failed</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount (LSL)</FormLabel>
                  <FormControl>
                    <Input placeholder="100.00" readOnly {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter the amount in Lesotho Loti
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="mobileNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mobile Number</FormLabel>
                  <FormControl>
                    <Input placeholder="5XXXXXXXX" {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter your M-Pesa registered mobile number
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="client_reference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Reference</FormLabel>
                  <FormControl>
                    <Input placeholder="Order #12345" readOnly {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter a reference for this payment
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full bg-red-600 hover:bg-red-500"
              disabled={isSubmitting}
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Pay with M-Pesa"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
