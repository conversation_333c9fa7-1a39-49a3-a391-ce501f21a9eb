"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Loader2 } from "lucide-react";
import { Bear<PERSON> } from "@/lib/constants";

import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { EcoCashPaymentResponse, PayLesothoPaymentResponse } from "@/lib/types";

// Configuration
const EXTERNAL_PAYMENT_API = `${process.env.NEXT_PUBLIC_EXTERNAL_PAYMENT_API}/api/external`;
const API_TOKEN =
  process.env.NEXT_PUBLIC_API_TOKEN ||
  "$10$KLstBWXvvqvOc91kSNUSs.Y9z2q3XFSHYcvHMLWab4ArDzbXB5tl2";

const formSchema = z.object({
  amount: z.string().min(1, { message: "Amount is required" }),
  mobileNumber: z
    .string()
    .min(8, { message: "Valid mobile number is required" }),
  client_reference: z.string().min(1, { message: "Reference is required" }),
  paymentMethod: z.enum(["mpesa", "ecocash"], {
    required_error: "Please select a payment method",
  }),
});

interface PaymentFormProps {
  onPaymentComplete?: (
    response: PayLesothoPaymentResponse | EcoCashPaymentResponse,
    method: "mpesa" | "ecocash"
  ) => void;
  defaultAmount?: string;
  defaultReference?: string;
}

export function PaymentForm({
  onPaymentComplete,
  defaultAmount = "",
  defaultReference = "",
}: PaymentFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [redirecting, setRedirecting] = useState(false);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      amount: defaultAmount,
      mobileNumber: "",
      client_reference: defaultReference,
      paymentMethod: "mpesa",
    },
  });

  const verifySession = async (
    sessionId: string,
    retries = 3
  ): Promise<boolean> => {
    for (let i = 0; i < retries; i++) {
      try {
        // Add a small delay before each retry
        if (i > 0) {
          await new Promise((resolve) => setTimeout(resolve, 1000));
        }

        const verifyResponse = await fetch(
          `${EXTERNAL_PAYMENT_API}/session/${sessionId}`
        );
        console.log(
          `Session verification attempt ${i + 1}:`,
          verifyResponse.status
        );

        if (verifyResponse.ok) {
          return true;
        }
      } catch (error) {
        console.error(`Verification attempt ${i + 1} failed:`, error);
      }
    }
    return false;
  };

  const onSubmit = async (values: z.infer<typeof formSchema>) => {
    setIsSubmitting(true);
    setError(null);

    try {
      // Generate a session ID that will be consistent
      const sessionId = `session_${Date.now()}`;
      const currentDomain = "https://suzannescareersolutions.com";

      console.log("Initiating payment with values:", {
        sessionId,
        amount: values.amount,
        reference: values.client_reference || sessionId,
        redirectUrl: `${currentDomain}/payment-complete`,
        paymentMethod: values.paymentMethod,
        apiEndpoint: `${EXTERNAL_PAYMENT_API}/payment`,
      });

      // Initialize payment session first
      const response = await fetch(`${EXTERNAL_PAYMENT_API}/payment`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${API_TOKEN}`,
        },
        body: JSON.stringify({
          amount: values.amount,
          reference: sessionId, // Use the session ID as the reference for consistency
          redirectUrl: `${currentDomain}/payment-complete`,
          webhookUrl: `${currentDomain}/api/callbacks`,
          metadata: {
            customerReference: values.client_reference,
            mobileNumber: values.mobileNumber,
            paymentMethod: values.paymentMethod,
          },
        }),
      });

      const data = await response.json();
      console.log("Payment initialization response:", data);

      if (!data.success) {
        throw new Error(
          data.message || data.error || "Failed to initialize payment"
        );
      }

      // Verify session exists with retries
      const isSessionValid = await verifySession(data.sessionId);
      if (!isSessionValid) {
        throw new Error(
          "Failed to verify payment session after multiple attempts. Please try again."
        );
      }

      // Store payment details before redirect
      const sessionDetails = {
        sessionId: data.sessionId,
        amount: values.amount,
        reference: values.client_reference,
      };
      console.log("Storing session details:", sessionDetails);

      localStorage.setItem("payment_session_id", data.sessionId);
      localStorage.setItem("payment_amount", values.amount);
      localStorage.setItem("payment_reference", values.client_reference);

      console.log("Redirecting to payment URL:", data.paymentUrl);
      setRedirecting(true);
      window.location.href = data.paymentUrl;
    } catch (error) {
      console.error("Payment submission error:", error);
      console.error(
        "message:",
        error instanceof Error ? error.message : "Failed to process payment"
      );
      setError(
        error instanceof Error
          ? error.message
          : "Payment system unavailable. Please try again later or contact support if the problem persists."
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  if (redirecting) {
    return (
      <div className="container mx-auto py-20 flex flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-lg text-muted-foreground">Redirecting...</p>
      </div>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Make a Payment</CardTitle>
        <CardDescription>
          Choose your payment method and enter your details
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Payment Failed</AlertTitle>
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="paymentMethod"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Method</FormLabel>
                  <FormControl>
                    <select
                      {...field}
                      className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                    >
                      <option value="mpesa">M-Pesa</option>
                      <option value="ecocash">EcoCash</option>
                    </select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="amount"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Amount (LSL)</FormLabel>
                  <FormControl>
                    <Input placeholder="100.00" {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter the amount in Lesotho Loti
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="mobileNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mobile Number</FormLabel>
                  <FormControl>
                    <Input placeholder="5XXXXXXXX" {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter your M-Pesa registered mobile number
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="client_reference"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Payment Reference</FormLabel>
                  <FormControl>
                    <Input placeholder="Order #12345" {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter a reference for this payment
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Processing...
                </>
              ) : (
                "Pay Now"
              )}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
