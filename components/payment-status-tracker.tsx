"use client"

import { useState, useEffect } from 'react'
import { Loader2 } from 'lucide-react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { PaymentStatusResponse } from '@/lib/types'
import { Bearer } from '@/lib/constants'

interface PaymentStatusTrackerProps {
  reference: string
  paymentMethod: 'mpesa' | 'ecocash'
  pollingInterval?: number
  onStatusChange?: (status: PaymentStatusResponse) => void
}

export function PaymentStatusTracker({ 
  reference,
  paymentMethod, 
  pollingInterval = 5000,
  onStatusChange 
}: PaymentStatusTrackerProps) {
  const [status, setStatus] = useState<PaymentStatusResponse | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isPolling, setIsPolling] = useState(true)
  const [transactionId, setTransactionId] = useState<string | undefined>(undefined)
  const [ecoCashDetails, setEcoCashDetails] = useState<string | undefined>(undefined)

  const fetchStatus = async () => {
    const env = process.env.NODE_ENV

    setIsLoading(true)
    try {
      if (paymentMethod === 'mpesa') {
        // Handle M-Pesa payment status check
        const baseUrl = env === 'production' ? process.env.NEXT_PUBLIC_API_BASE_URL : 'http://localhost:3000'; 
        const verifyResponse = await fetch(`${baseUrl}/api/payments/verify`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ reference }),
        });
        
        const verifyData = await verifyResponse.json();
        
        let statusResponse: PaymentStatusResponse;
        
        if (verifyData.status_code === 'INS-0') {
          setTransactionId(verifyData.transaction_id);
          statusResponse = {
            status: 'success',
            reference: verifyData.reference,
            message: verifyData.message,
            transaction_id: verifyData.transaction_id
          };
          setIsPolling(false);
        } else {
          const response = await fetch('/api/payments/status', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({ reference }),
          });
          
          statusResponse = await response.json();
        }
        setStatus(statusResponse);
      } else {
        // Handle EcoCash payment status check with updated format using POST method
        console.log('Checking EcoCash payment status for reference:', reference);
        const response = await fetch('/api/ecocash/verify', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${Bearer}`
          },
          body: JSON.stringify({ reference }),
        });
        
        const verifyData = await response.json();
        console.log('EcoCash verification result:', verifyData);
        
        // Handle EcoCash response format based on the updated API
        const statusResponse: PaymentStatusResponse = {
          status: verifyData.status_code === '200' ? 'success' : 'pending',
          reference: reference,
          message: verifyData.message || 'Payment verification in progress',
        };
        
        if (statusResponse.status === 'success') {
          setIsPolling(false);
          // Save the EcoCash message details from the reference field
          if (verifyData.reference) {
            setEcoCashDetails(verifyData.reference);
          }
          
          // Set transaction ID if available
          if (verifyData.transaction_reference) {
            setTransactionId(verifyData.transaction_reference);
          }
        }
        
        setStatus(statusResponse);
      }
      
      if (onStatusChange && status) {
        onStatusChange(status);
      }
    } catch (error) {
      console.error('Error checking payment status:', error)
      setStatus({
        status: 'error',
        message: 'Failed to check payment status. Please try again.'
      });
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    // Initial check
    fetchStatus()
    
    // Set up polling
    let intervalId: NodeJS.Timeout | null = null
    
    if (isPolling) {
      intervalId = setInterval(fetchStatus, pollingInterval)
    }
    
    // Clean up
    return () => {
      if (intervalId) {
        clearInterval(intervalId)
      }
    }
  }, [reference, isPolling, pollingInterval])

  const getStatusDisplay = () => {
    if (!status) return null
    
    switch (status.status) {
      case 'success':
        return (
          <Alert className="bg-green-50 border-green-200">
            <AlertTitle>Payment Successful</AlertTitle>
            <AlertDescription>
              Your payment has been processed successfully.
              <div className="mt-2">
                <strong>Reference:</strong> {status.reference}
                {transactionId && (
                  <div className="mt-1">
                    <strong>Transaction ID:</strong> {transactionId}
                  </div>
                )}
                {ecoCashDetails && paymentMethod === 'ecocash' && (
                  <div className="mt-2 text-sm bg-gray-50 p-3 rounded-md border border-gray-200">
                    {/* {ecoCashDetails} */}
                    <div className="text-green-600">Payment Successful</div>
                  </div>
                )}
              </div>
            </AlertDescription>
          </Alert>
        )
      case 'error':
        return (
          <Alert variant="destructive">
            <AlertTitle>Payment Failed</AlertTitle>
            <AlertDescription>{status.message}</AlertDescription>
          </Alert>
        )
      case 'pending':
        return (
          <Alert className="bg-yellow-50 border-yellow-200">
            <AlertTitle>Payment Pending</AlertTitle>
            <AlertDescription>
              Your payment is being processed. Please wait or check your mobile phone for payment confirmation.
            </AlertDescription>
          </Alert>
        )
      default:
        return null
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader>
        <CardTitle>Payment Status</CardTitle>
        <CardDescription>
          Tracking payment reference: {reference}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <span className="ml-2">Checking payment status...</span>
          </div>
        ) : (
          <>
            {getStatusDisplay()}
            
            {status?.status === 'pending' && (
              <div className="mt-4 flex justify-end">
                <Button 
                  variant="outline" 
                  onClick={fetchStatus}
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Checking...
                    </>
                  ) : (
                    'Check Again'
                  )}
                </Button>
              </div>
            )}
          </>
        )}
      </CardContent>
    </Card>
  )
}