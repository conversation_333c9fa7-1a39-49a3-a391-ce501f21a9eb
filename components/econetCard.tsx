import React from "react";
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardHeader,
  CardTitle,
} from "./ui/card";
import Link from "next/link";
import { But<PERSON> } from "./ui/button";
import Image from "next/image";

function EconetCard() {
  return (
    <Card className="shadow-xl border-none">
      <CardHeader>
        <CardTitle className="flex flex-col justify-center items-center">
          <Image src={"/etl-logo.png"} alt="etl-logo" width={260} height={30} />
          <span>EcoCash Payment</span>
        </CardTitle>
        <CardDescription className="text-center">
          Make payments using EcoCash mobile money
        </CardDescription>
      </CardHeader>
      <CardContent>
        <p className="text-center">
          Use our secure payment form to make payments through EcoCash. Simple,
          secure, and convenient for all transactions.
        </p>
      </CardContent>
      <CardFooter className="">
        <Link href="/payment/ecocash" className="w-full">
          <Button className="w-full bg-blue-900 hover:bg-blue-800">
            Pay with EcoCash
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}

export default EconetCard;
