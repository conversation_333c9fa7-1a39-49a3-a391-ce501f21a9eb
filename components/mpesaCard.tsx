import React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "./ui/card";
import Link from "next/link";
import { Button } from "./ui/button";

function MpesaCard() {
  return (
    <Card className="shadow-xl border-none">
      <CardHeader>
        <CardTitle className="flex flex-col gap-4 items-center">
          <svg
            version="1.1"
            viewBox="0 0 284 284"
            // Title="Vodafone Logo"
            className="w-8 h-8 md:w-10 md:h-10 bg-vodafone_white"
          >
            <g
              id="Logos-/-Vodafone-Logo-/-Red"
              stroke="none"
              strokeWidth="1"
              fill="none"
              fillRule="evenodd"
            >
              <rect fill="#FFFFFF" x="0" y="0" width="48" height="48"></rect>
              <path
                d="M284.001,142 C284.001,220.425 220.425,284 142.001,284 C63.576,284 0,220.425 0,142 C0,63.575 63.576,0 142.001,0 C220.425,0 284.001,63.575 284.001,142"
                id="Oval"
                fill="#E60000"
              ></path>
              <path
                d="M143.126,221.181975 C104.11,221.3109 63.5199,188.012 63.3419537,134.543 C63.2243,99.183 82.3013,65.147 106.686,44.954 C130.471,25.259 163.056,12.62 192.608,12.5216173 C196.412,12.509 200.39,12.826 202.826,13.65 C176.987,19.009 156.422,43.056 156.512,70.338 C156.515,71.24 156.597,72.203 156.686,72.652 C199.924,83.183 219.552,109.272 219.673371,145.372 C219.792,181.47 191.293,221.0213 143.126,221.181975"
                id="Speechmark"
                fill="#FFFFFF"
              ></path>
            </g>
          </svg>
          M-Pesa Payment
        </CardTitle>
        <CardDescription className="text-center">
          Make payments using M-Pesa mobile money
        </CardDescription>
      </CardHeader>
      <CardContent className="mb-[2.95rem] lg:mb-[1.5rem]">
        <p className="text-center">
          Use our secure payment form to make payments through M-Pesa. Fast,
          reliable, and provides real-time status updates.
        </p>
      </CardContent>
      <CardFooter className="">
        <Link href="/payment/mpesa" className="w-full">
          <Button className="w-full bg-red-600 hover:bg-red-500">
            Pay with M-Pesa
          </Button>
        </Link>
      </CardFooter>
    </Card>
  );
}

export default MpesaCard;
