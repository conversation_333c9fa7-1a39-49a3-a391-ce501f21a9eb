# Pay-Up: Mobile Money Payment Gateway
vercel
Pay-Up is a modern payment gateway that seamlessly integrates MPesa and EcoCash mobile money services into your applications.

## Features

- **Multiple Payment Methods**: Support for MPesa and EcoCash mobile money services
- **API Integration**: Simple REST API for external application integration
- **Webhook Support**: Real-time notifications when payments are completed
- **Status Tracking**: Track payment status in real-time
- **Transaction History**: Keep records of all payment transactions
- **User-friendly Interface**: Clean and intuitive payment flow

## Quick Start

### Prerequisites

- Node.js 18.x or higher
- PostgreSQL database
- API credentials for MPesa and EcoCash services

### Installation

1. Clone the repository:
```bash
git clone https://github.com/yourusername/pay-up.git
cd pay-up
```

2. Install dependencies:
```bash
npm install
# or
pnpm install
```

3. Set up environment variables:
```
DATABASE_URL="postgresql://username:password@localhost:5432/payup"
PAY_LESOTHO_MERCHANT_ID="your_mpesa_merchant_id"
ECOCASH_MERCHANT_ID="your_ecocash_merchant_id"
ECOCASH_MERCHANT_NAME="your_ecocash_merchant_name"
```

4. Run database migrations:
```bash
npx prisma migrate dev
```

5. Start the development server:
```bash
npm run dev
# or
pnpm dev
```

## Integration Guide

### Direct Integration

Integrate the payment gateway directly into your Next.js application:

```jsx
import { PaymentGateway } from 'pay-up'

export default function CheckoutPage() {
  return (
    <div>
      <h1>Checkout</h1>
      <PaymentGateway 
        amount="100.00"
        reference="Order #12345"
        onSuccess={(result) => console.log('Payment successful', result)}
        onFailure={(error) => console.log('Payment failed', error)}
      />
    </div>
  )
}
```

### External API Integration

For applications built with other frameworks, use our RESTful API:

1. Create a payment session:

```javascript
const response = await fetch('https://payments.pay-up.com/api/external/payment', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    amount: '100.00',
    reference: 'Order #12345',
    redirectUrl: 'https://your-app.com/payment-complete',
    webhookUrl: 'https://your-app.com/webhooks/payment'
  })
})

const { paymentUrl } = await response.json()
// Redirect user to paymentUrl
```

2. Handle the webhook notification:

```javascript
app.post('/webhooks/payment', (req, res) => {
  const { sessionId, status, paymentReference, transactionId } = req.body
  
  // Update your order status
  if (status === 'completed') {
    // Payment successful
  }
  
  res.json({ received: true })
})
```

## Documentation

For detailed documentation, see the [full documentation](./docs/payment-system.md).

## License
MIT

## Support

For support, email <EMAIL> or open an issue in this repository.
Minor changes
To satisfy vercel
