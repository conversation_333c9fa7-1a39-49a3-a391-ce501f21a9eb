"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { PaymentStatusTracker } from "@/components/payment-status-tracker";
import { EcoCashPaymentForm } from "@/components/ui/ecocash-payment-form";
import { MPesaPaymentForm } from "@/components/ui/mpesa-payment-form";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ExternalPaymentStatus } from "@prisma/client";
import { Loader2, ArrowLeft } from "lucide-react";
import Image from "next/image";
import BrandIcons from "@/components/ui/icons";
import { checkPaymentMethodAvailability } from "@/lib/paymentMethodsAvailabilty";

type ExternalSessionData = {
  id: string;
  sessionId: string;
  amount: string;
  reference: string;
  redirectUrl: string;
  status: ExternalPaymentStatus;
  userId?: string;
  appId?: string;
  originatingDomain?: string;
  selectedPaymentMethod?: string;
  completedPaymentRef?: string;
  transactionId?: string;
};

type PaymentResponse = {
  reference: string;
  status?: string;
  transaction_id?: string;
};

export default function ExternalPaymentPage() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;

  const [session, setSession] = useState<ExternalSessionData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [paymentMethod, setPaymentMethod] = useState<"ecocash" | "mpesa">(
    "mpesa"
  );
  const [paymentResponse, setPaymentResponse] =
    useState<PaymentResponse | null>(null);
  const [redirecting, setRedirecting] = useState(false);
  const [mpesaAvailable, setMpesaAvailable] = useState(true);
  const [ecocashAvailable, setEcocashAvailable] = useState(true);

  // Load session data
  useEffect(() => {
    async function fetchSessionData() {
      try {
        // Fetch existing session
        // const response = await fetch(`/api/external/session/${sessionId}`)

        const [response, mpesaAvailabilityInfo, EcoCashAvailabilityInfo] =
          await Promise.all([
            fetch(`/api/external/session/${sessionId}`),
            checkPaymentMethodAvailability("MPESA"),
            checkPaymentMethodAvailability("ECOCASH"),
          ]);

        if (mpesaAvailabilityInfo) {
          if (Array.isArray(mpesaAvailabilityInfo)) {
            setMpesaAvailable(mpesaAvailabilityInfo[0].isActive);
          } else {
            setMpesaAvailable(mpesaAvailabilityInfo.available);
          }
        }

        if (EcoCashAvailabilityInfo) {
          if (Array.isArray(EcoCashAvailabilityInfo)) {
            setEcocashAvailable(EcoCashAvailabilityInfo[0].isActive);
          } else {
            setEcocashAvailable(EcoCashAvailabilityInfo.available);
          }
        }

        if (response.ok) {
          const data = await response.json();
          if (data.success && data.session) {
            setSession(data.session);

            // If the session is already completed, set the payment response
            if (
              data.session.status === "COMPLETED" &&
              data.session.completedPaymentRef
            ) {
              setPaymentResponse({
                reference: data.session.completedPaymentRef,
                transaction_id: data.session.transactionId,
              });
              setPaymentMethod(
                (data.session.selectedPaymentMethod as any) || "mpesa"
              );
            }
          } else {
            throw new Error(data.error || "Invalid session data");
          }
        } else if (response.status === 404) {
          throw new Error(
            "Payment session not found. The link may be invalid or expired."
          );
        } else {
          throw new Error("Failed to load payment session");
        }
      } catch (err: any) {
        console.error("Error fetching session:", err);
        console.error(
          "message:",
          err instanceof Error ? err.message : "Failed to load payment data"
        );
        setError("Failed to load payment data");
      } finally {
        setLoading(false);
      }
    }

    fetchSessionData();
  }, [sessionId]);

  // Handle payment completion
  const handlePaymentComplete = async (response: PaymentResponse) => {
    setPaymentResponse(response);

    // Update session with payment method and reference
    try {
      await fetch(`/api/external/session/${sessionId}/update`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          paymentMethod,
          paymentReference: response.reference,
          transactionId: response.transaction_id,
        }),
      });
    } catch (err) {
      console.error("Error updating session:", err);
    }
  };

  // Handle payment status change
  const handleStatusChange = async (status: any) => {
    if (status?.status === "success" && session?.redirectUrl) {
      // Add a small delay to allow for final status updates
      setTimeout(() => {
        // Redirect to the external app with transaction details
        const redirectUrl = new URL(session.redirectUrl);
        redirectUrl.searchParams.append("sessionId", sessionId);
        redirectUrl.searchParams.append("status", "success");
        redirectUrl.searchParams.append(
          "reference",
          paymentResponse?.reference || ""
        );
        if (status.transaction_id || paymentResponse?.transaction_id) {
          redirectUrl.searchParams.append(
            "transactionId",
            status.transaction_id || paymentResponse?.transaction_id || ""
          );
        }

        // Add user and app identifiers if available
        if (session.userId) {
          redirectUrl.searchParams.append("userId", session.userId);
        }
        if (session.appId) {
          redirectUrl.searchParams.append("appId", session.appId);
        }
        setRedirecting(true);
        window.location.href = redirectUrl.toString();
      }, 2000);
    }
  };

  // Handle payment method selection
  const handleMethodSelect = (method: "ecocash" | "mpesa") => {
    setPaymentMethod(method);
  };

  // Handle cancellation and return to originating site
  const handleCancel = () => {
    if (session?.redirectUrl) {
      const redirectUrl = new URL(session.redirectUrl);
      redirectUrl.searchParams.append("sessionId", sessionId);
      redirectUrl.searchParams.append("status", "cancelled");

      // Add user and app identifiers if available
      if (session.userId) {
        redirectUrl.searchParams.append("userId", session.userId);
      }
      if (session.appId) {
        redirectUrl.searchParams.append("appId", session.appId);
      }

      // Update session status to cancelled (optional)
      try {
        fetch(`/api/external/session/${sessionId}/update`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            status: "CANCELLED",
          }),
        }).finally(() => {
          setRedirecting(true);
          // Redirect regardless of API success
          window.location.href = redirectUrl.toString();
        });
      } catch (err) {
        console.error("Error updating session status:", err);
        setRedirecting(true);
        // Redirect anyway even if update fails
        window.location.href = redirectUrl.toString();
      }
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto py-20 flex flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-lg text-muted-foreground">
          Loading payment session...
        </p>
      </div>
    );
  }

  if (redirecting) {
    return (
      <div className="container mx-auto py-20 flex flex-col items-center justify-center">
        <Loader2 className="h-12 w-12 animate-spin text-primary" />
        <p className="mt-4 text-lg text-muted-foreground">Redirecting...</p>
      </div>
    );
  }

  if (error || !session) {
    return (
      <div className="container mx-auto py-10 px-4">
        <Alert variant="destructive" className="max-w-lg mx-auto">
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            {error ||
              "Invalid payment session. Please try again or contact support."}
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10 px-4">
      <div className="max-w-xl mx-auto text-center">
        <div className="w-full flex justify-center mb-5">
          <Image
            src={"/paylesotho-logo.png"}
            alt="pay-lesotho-logo"
            width={300}
            height={30}
            className="block md:hidden"
          />
        </div>
        <h1 className="text-3xl font-bold mb-2">Complete Your Payment</h1>
        <p className="text-muted-foreground">
          {session?.reference} • {session?.amount}
        </p>
        {session?.originatingDomain && (
          <p className="text-sm text-muted-foreground mt-2">
            Payment requested by: {session?.originatingDomain}
          </p>
        )}

        {/* Return to originating site link */}
        <div className="mt-4 ">
          <Button
            variant="outline"
            size="sm"
            className="text-muted-foreground"
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Return to {session?.originatingDomain || "previous site"}
          </Button>
          <div className="">
            <BrandIcons paymentMethod={paymentMethod} />
          </div>
        </div>
      </div>

      <div className="max-w-md mx-auto ">
        {paymentResponse?.reference ? (
          <PaymentStatusTracker
            reference={paymentResponse.reference}
            paymentMethod={paymentMethod}
            onStatusChange={handleStatusChange}
          />
        ) : (
          <>
            <Card className="border-none">
              <CardHeader>
                <CardTitle>Choose Payment Method</CardTitle>
                <CardDescription>
                  Select your preferred mobile money provider
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Tabs
                  defaultValue="mpesa"
                  onValueChange={(value) => handleMethodSelect(value as any)}
                >
                  <TabsList className="grid w-full grid-cols-2">
                    <TabsTrigger
                      value="mpesa"
                      className="data-[state=active]:bg-red-500 data-[state=active]:text-white"
                    >
                      M-Pesa
                    </TabsTrigger>
                    <TabsTrigger
                      value="ecocash"
                      className="data-[state=active]:bg-blue-900 data-[state=active]:text-white"
                    >
                      EcoCash
                    </TabsTrigger>
                  </TabsList>
                  <TabsContent value="mpesa" className="mt-4">
                    {mpesaAvailable ? (
                      <MPesaPaymentForm
                        // @ts-ignore
                        onPaymentComplete={handlePaymentComplete}
                        defaultAmount={session?.amount}
                        defaultReference={session?.reference}
                      />
                    ) : (
                      <Alert variant="destructive" className="mb-4">
                        <AlertTitle>Payment Method Unavailable</AlertTitle>
                        <AlertDescription>
                          M-Pesa payments are currently unavailable until
                          further notice. We're deeply sorry for any
                          inconvenience caused. {ecocashAvailable && "Please use EcoCash instead."}
                        </AlertDescription>
                      </Alert>
                    )}
                  </TabsContent>
                  <TabsContent value="ecocash" className="mt-4">
                    {ecocashAvailable ? (
                      <EcoCashPaymentForm
                        onPaymentComplete={handlePaymentComplete}
                        defaultAmount={session?.amount}
                        defaultReference={session?.reference}
                      />
                    ) : (
                      <Alert variant="destructive" className="mb-4">
                        <AlertTitle>Payment Method Unavailable</AlertTitle>
                        <AlertDescription>
                          EcoCash payments are currently unavailable until
                          further notice. We're deeply sorry for any
                          inconvenience caused. {mpesaAvailable && "Please use M-Pesa instead."}
                        </AlertDescription>
                      </Alert>
                    )}
                  </TabsContent>
                </Tabs>
              </CardContent>
              <CardFooter>
                <Button
                  variant="ghost"
                  className="w-full text-muted-foreground mt-2"
                  onClick={handleCancel}
                >
                  Cancel payment and return
                </Button>
              </CardFooter>
            </Card>
          </>
        )}
      </div>
    </div>
  );
}

// Ensure that this page is dynamically rendered even with static site generation
export const dynamic = "force-dynamic";
