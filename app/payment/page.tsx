import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'

export default function PaymentPage() {
  return (
    <div className="container mx-auto py-10 px-4">
      <div className="max-w-4xl mx-auto text-center mb-10">
        <h1 className="text-4xl font-bold mb-4">Choose Payment Method</h1>
        <p className="text-xl text-muted-foreground">
          Select your preferred mobile money provider
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>M-Pesa Payment</CardTitle>
            <CardDescription>
              Pay using M-Pesa mobile money
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              Use M-Pesa for fast and secure mobile money payments.
              Perfect for Pay Lesotho transactions.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/payment/mpesa" className="w-full">
              <Button className="w-full">Continue with M-Pesa</Button>
            </Link>
          </CardFooter>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>EcoCash Payment</CardTitle>
            <CardDescription>
              Pay using EcoCash mobile money
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>
              Use EcoCash for convenient and reliable mobile money payments.
              Quick confirmation and transaction tracking.
            </p>
          </CardContent>
          <CardFooter>
            <Link href="/payment/ecocash" className="w-full">
              <Button className="w-full" variant="outline">Continue with EcoCash</Button>
            </Link>
          </CardFooter>
        </Card>
      </div>
    </div>
  )
}