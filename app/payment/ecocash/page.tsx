"use client";

import { useEffect, useState } from "react";
import { EcoCashPaymentForm } from "@/components/ui/ecocash-payment-form";
import { PaymentStatusTracker } from "@/components/payment-status-tracker";
import { EcoCashPaymentResponse } from "@/lib/types";
import Image from "next/image";
import { checkPaymentMethodAvailability } from "@/lib/paymentMethodsAvailabilty";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function EcoCashPaymentPage() {
  const [paymentResponse, setPaymentResponse] =
    useState<EcoCashPaymentResponse | null>(null);
  const [available, setAvailable] = useState(true);

  const handlePaymentComplete = (response: EcoCashPaymentResponse) => {
    setPaymentResponse(response);
  };

  useEffect(() => {
    const checkAvailability = async () => {
      const availability = await checkPaymentMethodAvailability("ECOCASH");
      if (Array.isArray(availability)) {
        setAvailable(availability[0].isActive);
        return;
      }
      setAvailable(availability.available);
    };

    checkAvailability();
  }, []);

  return (
    <div className="container mx-auto py-10 px-4 h-screen flex flex-col items-center justify-center">
      {/* <h1 className="text-3xl font-bold text-center mb-8">EcoCash Payment</h1> */}

      <Image src={"/etl-logo.png"} alt="etl-logo" width={300} height={30} />

      <div className="max-w-md mx-auto">
        {paymentResponse?.reference ? (
          <PaymentStatusTracker
            reference={paymentResponse.reference}
            paymentMethod="ecocash"
          />
        ) : available ? (
          <EcoCashPaymentForm
            onPaymentComplete={handlePaymentComplete}
            defaultAmount="1.00"
            defaultReference="Product Purchase"
          />
        ) : (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Payment Method Unavailable</AlertTitle>
            <AlertDescription>
              EcoCash payments are currently unavailable until further notice.
              We're deeply sorry for any inconvenience caused.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}
