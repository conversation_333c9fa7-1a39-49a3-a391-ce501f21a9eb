"use client";

import { useEffect, useState } from "react";
import { MPesaPaymentForm } from "@/components/ui/mpesa-payment-form";
import { PaymentStatusTracker } from "@/components/payment-status-tracker";
import { PayLesothoPaymentResponse } from "@/lib/types";
import { checkPaymentMethodAvailability } from "@/lib/paymentMethodsAvailabilty";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";

export default function MPesaPaymentPage() {
  const [paymentResponse, setPaymentResponse] =
    useState<PayLesothoPaymentResponse | null>(null);
  const [available, setAvailable] = useState(true);

  const handlePaymentComplete = (response: PayLesothoPaymentResponse) => {
    setPaymentResponse(response);
  };

  useEffect(() => {
    const checkAvailability = async () => {
      const availability = await checkPaymentMethodAvailability("MPESA");
      if (Array.isArray(availability)) {
        setAvailable(availability[0].isActive);
        return;
      }
      setAvailable(availability.available);
    };

    checkAvailability();
  }, []);

  return (
    <div className="container mx-auto py-10 px-4 h-screen flex flex-col items-center justify-center">
      {/* <h1 className="text-3xl font-bold text-center mb-8">M-Pesa Payment</h1> */}
      <svg
        version="1.1"
        viewBox="0 0 284 284"
        // Title="Vodafone Logo"
        className="w-8 h-8 mb-5 md:w-10 md:h-10 bg-vodafone_white"
      >
        <g
          id="Logos-/-Vodafone-Logo-/-Red"
          stroke="none"
          strokeWidth="1"
          fill="none"
          fillRule="evenodd"
        >
          <rect fill="#FFFFFF" x="0" y="0" width="48" height="48"></rect>
          <path
            d="M284.001,142 C284.001,220.425 220.425,284 142.001,284 C63.576,284 0,220.425 0,142 C0,63.575 63.576,0 142.001,0 C220.425,0 284.001,63.575 284.001,142"
            id="Oval"
            fill="#E60000"
          ></path>
          <path
            d="M143.126,221.181975 C104.11,221.3109 63.5199,188.012 63.3419537,134.543 C63.2243,99.183 82.3013,65.147 106.686,44.954 C130.471,25.259 163.056,12.62 192.608,12.5216173 C196.412,12.509 200.39,12.826 202.826,13.65 C176.987,19.009 156.422,43.056 156.512,70.338 C156.515,71.24 156.597,72.203 156.686,72.652 C199.924,83.183 219.552,109.272 219.673371,145.372 C219.792,181.47 191.293,221.0213 143.126,221.181975"
            id="Speechmark"
            fill="#FFFFFF"
          ></path>
        </g>
      </svg>

      <div className="max-w-md mx-auto">
        {paymentResponse?.reference ? (
          <PaymentStatusTracker
            reference={paymentResponse.reference}
            paymentMethod="mpesa"
          />
        ) : available ? (
          <MPesaPaymentForm
            onPaymentComplete={handlePaymentComplete}
            defaultAmount="1.00"
            defaultReference="Product Purchase"
          />
        ) : (
          <Alert variant="destructive" className="mb-4">
            <AlertTitle>Payment Method Unavailable</AlertTitle>
            <AlertDescription>
              M-Pesa payments are currently unavailable until further notice.
              We're deeply sorry for any inconvenience caused.
            </AlertDescription>
          </Alert>
        )}
      </div>
    </div>
  );
}
