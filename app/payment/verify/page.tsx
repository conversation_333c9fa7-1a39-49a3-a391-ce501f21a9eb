"use client"

import { useState } from 'react'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { PaymentStatusTracker } from '@/components/payment-status-tracker'

// Form validation schema
const formSchema = z.object({
  reference: z.string().min(1, "Reference is required"),
  paymentMethod: z.enum(['ecocash', 'mpesa'], {
    required_error: "Please select a payment method"
  })
})

type FormValues = z.infer<typeof formSchema>

export default function VerifyPaymentPage() {
  const [paymentDetails, setPaymentDetails] = useState<FormValues | null>(null)
  
  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      reference: "",
      paymentMethod: "ecocash"
    }
  })

  // Form submission handler
  function onSubmit(values: FormValues) {
    setPaymentDetails(values)
  }

  return (
    <div className="container mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold text-center mb-8">Verify Payment</h1>
      
      <div className="max-w-md mx-auto">
        {paymentDetails ? (
          <>
            <PaymentStatusTracker 
              reference={paymentDetails.reference} 
              paymentMethod={paymentDetails.paymentMethod}
            />
            <div className="mt-4">
              <Button 
                variant="outline" 
                onClick={() => setPaymentDetails(null)}
              >
                Check Another Payment
              </Button>
            </div>
          </>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Verify Payment Status</CardTitle>
              <CardDescription>
                Enter your payment reference number to check its status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="reference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Reference</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter payment reference..." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="paymentMethod"
                    render={({ field }) => (
                      <FormItem className="space-y-3">
                        <FormLabel>Payment Method</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="flex flex-col space-y-1"
                          >
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="ecocash" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                EcoCash
                              </FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-3 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="mpesa" />
                              </FormControl>
                              <FormLabel className="font-normal">
                                M-Pesa
                              </FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button type="submit" className="w-full">
                    Verify Payment
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}