"use client"

import { useState } from 'react'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { PaymentStatusTracker } from '@/components/payment-status-tracker'

// Form validation schema
const formSchema = z.object({
  reference: z.string().min(1, "Reference number is required")
})

type FormValues = z.infer<typeof formSchema>

export default function EcoCashVerifyPage() {
  const [reference, setReference] = useState<string | null>(null)
  
  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      reference: ""
    }
  })

  // Form submission handler
  function onSubmit(values: FormValues) {
    setReference(values.reference)
  }

  return (
    <div className="container mx-auto py-10 px-4">
      <h1 className="text-3xl font-bold text-center mb-8">Verify EcoCash Payment</h1>
      
      <div className="max-w-md mx-auto">
        {reference ? (
          <>
            <PaymentStatusTracker 
              reference={reference} 
              paymentMethod="ecocash"
            />
            <div className="mt-4">
              <Button 
                variant="outline" 
                onClick={() => setReference(null)}
              >
                Check Another Payment
              </Button>
            </div>
          </>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>Verify EcoCash Payment</CardTitle>
              <CardDescription>
                Enter your EcoCash payment reference number to check its status
              </CardDescription>
            </CardHeader>
            <CardContent>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                  <FormField
                    control={form.control}
                    name="reference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>EcoCash Reference Number</FormLabel>
                        <FormControl>
                          <Input placeholder="Enter reference number..." {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <Button type="submit" className="w-full">
                    Verify Payment
                  </Button>
                </form>
              </Form>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}