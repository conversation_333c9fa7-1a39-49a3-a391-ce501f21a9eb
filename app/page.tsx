import Image from "next/image";
import MpesaCard from "@/components/mpesaCard";
import EconetCard from "@/components/econetCard";

export default function Home() {
  return (
    <div className="container mx-auto py-10 px-4">
      <div className="max-w-4xl mx-auto text-center mb-10">
        <Image
          src={"/paylesotho-logo.png"}
          alt="pay-lesotho-logo"
          width={300}
          height={30}
          className="mx-auto"
        />
        <h1 className="text-4xl font-bold mb-4 ">
          {/* <span className="text-green-500">Pay Lesotho</span>  */}
          Integration
        </h1>
        <p className="text-xl text-muted-foreground">
          A seamless payment solution for businesses in Lesotho
        </p>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 max-w-4xl mx-auto my-auto mb-10">
        <EconetCard />
        <MpesaCard />
      </div>
    </div>
  );
}
