import './globals.css';
import type { Metadata } from 'next';
import { DM_Sans } from "next/font/google";

const font = DM_Sans({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: 'Pay Lesotho Integration',
  description: 'Next.js payment integration for Pay Lesotho'
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={font.className}>{children}</body>
    </html>
  );
}