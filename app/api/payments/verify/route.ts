import { verifyTransaction } from "@/lib/verifyTransaction";
import { prisma } from "@/lib/db";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    if (!body.reference) {
      return NextResponse.json(
        { error: 'Payment reference is required' },
        { status: 400 }
      );
    }
    
    // Find the payment record in database
    const payment = await prisma.mpesaPayment.findUnique({
      where: { reference: body.reference }
    });

    if (!payment) {
      return NextResponse.json(
        { error: 'Payment record not found' },
        { status: 404 }
      );
    }
    
    // Get verification result from M-Pesa
    const verificationResult = await verifyTransaction(body.reference);
    
    // Update the payment record based on verification result
    if (verificationResult.status_code === 'INS-0' || verificationResult.status_code === 'success') {
      // Payment successful
      await prisma.mpesaPayment.update({
        where: { id: payment.id },
        data: {
          status: 'COMPLETED',
          statusMessage: verificationResult.message,
          transactionId: verificationResult.transaction_id,
          verificationReference: body.reference,
          verificationDate: new Date()
        }
      });
      
      // Store the callback data
      await prisma.paymentCallback.create({
        data: {
          paymentId: payment.id,
          statusCode: verificationResult.status_code,
          message: verificationResult.message,
          rawPayload: verificationResult as any
        }
      });
      
    } else {
      // Payment verification failed or payment is still pending
      const status = verificationResult.status_code === 'INS-666' ? 'PENDING' : 'FAILED';
      
      await prisma.mpesaPayment.update({
        where: { id: payment.id },
        data: {
          status,
          statusMessage: verificationResult.message || verificationResult.error,
          verificationDate: new Date()
        }
      });
      
      // Store the callback data even for failed verifications
      await prisma.paymentCallback.create({
        data: {
          paymentId: payment.id,
          statusCode: verificationResult.status_code,
          message: verificationResult.message || verificationResult.error || 'Verification failed',
          rawPayload: verificationResult as any
        }
      });
    }
    
    // Return the verification result with the payment ID
    return NextResponse.json({
      ...verificationResult,
      paymentId: payment.id,
      paymentStatus: verificationResult.status_code === 'INS-0' ? 'COMPLETED' : 
                     verificationResult.status_code === 'INS-666' ? 'PENDING' : 'FAILED'
    });
    
  } catch (error) {
    console.error('Payment verification error:', error);
    return NextResponse.json(
      { error: 'Failed to verify payment' },
      { status: 500 }
    );
  }
}