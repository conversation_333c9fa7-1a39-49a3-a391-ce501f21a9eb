import { NextRequest, NextResponse } from "next/server";
import { initiatePayment } from '@/lib/pay-lesotho'
import { PayLesothoPaymentRequest } from "@/lib/types";
import { prisma } from "@/lib/db";

export async function POST(request: NextRequest) {
  console.log('POST /api/payments - Request received');
  try {
    const body = await request.json() as PayLesothoPaymentRequest
    console.log('Request body:', JSON.stringify(body, null, 2));
    
    // Validate required fields
    if (!body.amount || !body.mobileNumber || !body.client_reference) {
      console.error('Validation failed - Missing required fields:', { 
        amount: !!body.amount, 
        mobileNumber: !!body.mobileNumber, 
        client_reference: !!body.client_reference 
      });
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }
    
    console.log('Creating initial payment record in database');
    // Create initial payment record in database
    const payment = await prisma.mpesaPayment.create({
      data: {
        amount: body.amount,
        mobileNumber: body.mobileNumber,
        clientReference: body.client_reference,
        status: 'INITIATED'
      }
    });
    console.log('Payment record created:', payment.id);
    
    // Process payment through Pay Lesotho
    console.log('Initiating payment with Pay Lesotho');
    const response = await initiatePayment(body)
    console.log('Pay Lesotho response:', JSON.stringify(response, null, 2));
    
    if (response.status_code === 'INS-0') {
      console.log('Payment initiated successfully');
      // Update payment with successful initiation details
      await prisma.mpesaPayment.update({
        where: { id: payment.id },
        data: {
          reference: response.reference,
          status: 'PENDING',
          statusMessage: response.message
        }
      });
      
      return NextResponse.json({
        ...response,
        paymentId: payment.id
      });
    } else {
      console.error('Payment initiation failed:', response);
      // Update payment with failure details
      await prisma.mpesaPayment.update({
        where: { id: payment.id },
        data: {
          status: 'FAILED',
          statusMessage: response.message || response.error
        }
      });
      
      return NextResponse.json(
        { error: response.message || 'Payment failed', paymentId: payment.id },
        { status: 400 }
      )
    }
  } catch (error) {
    console.error('Payment processing error:', error instanceof Error ? error.message : error);
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack available');
    return NextResponse.json(
      { error: 'Failed to process payment' },
      { status: 500 }
    )
  }
}