import { NextResponse } from 'next/server';
import { ECOCASH_API_URL, Bearer } from '@/lib/constants';
import { 
  findEcocashPaymentByReference, 
  updateEcocashPaymentVerification, 
  logEcocashCallback 
} from '@/lib/db/ecocash';
import { EcocashPaymentStatus } from '@prisma/client';

export async function POST(request: Request) {
  try {
    const body = await request.json();
    const { reference } = body;

    if (!reference) {
      console.log('Missing reference in verification request');
      return NextResponse.json(
        { error: 'Reference is required', status_code: '400' },
        { status: 400 }
      );
    }

    console.log('Verifying EcoCash transaction with reference:', reference);
    
    // Look up the payment in the database
    const payment = await findEcocashPaymentByReference(reference);
    
    if (!payment) {
      console.log('Payment with reference not found in database:', reference);
      return NextResponse.json({
        status_code: '404',
        message: 'Payment reference not found in our records'
      }, { status: 404 });
    }
    
    // Fix URL construction to avoid duplication
    const verifyUrl = `https://api.paylesotho.co.ls/api/v1/econet/verify/${reference}`;
    
    console.log(`Verification URL: ${verifyUrl}`);

    const response = await fetch(verifyUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer Bearer $10$KLstBWXvvqvOc91kSNUSs.Y9z2q3XFSHYcvHMLWab4ArDzbXB5tl2`,
        'Accept': 'application/json'
      }
    });

    console.log('EcoCash verify API response status:', response.status);
    
    // Check for unauthorized response first
    if (response.status === 401) {
      console.log('Authentication failed:', {
        status: response.status,
        statusText: response.statusText
      });
      
      // Log the failed verification attempt
      await logEcocashCallback(
        payment.id,
        '401',
        'Unauthorized - Invalid API credentials',
        { status: response.status, statusText: response.statusText }
      );
      
      return NextResponse.json({
        status_code: '401',
        message: 'Unauthorized - Invalid API credentials'
      }, { status: 401 });
    }

    let responseText;
    let data;

    try {
      responseText = await response.text();
      console.log('Raw verification response:', responseText);

      // Only try to parse if we have a response
      if (responseText && responseText.trim()) {
        data = JSON.parse(responseText);
      } else {
        throw new Error('Empty response from verification endpoint');
      }
    } catch (parseError) {
      console.error('Response parsing error:', parseError);
      console.error('Raw response:', responseText);
      
      // Log the error in our database
      await logEcocashCallback(
        payment.id,
        '400',
        'Invalid response from payment provider',
        { error: parseError instanceof Error ? parseError.message : 'JSON Parse Error', raw_response: responseText }
      );
      
      return NextResponse.json({
        status_code: '400',
        message: 'Invalid response from payment provider',
        error: parseError instanceof Error ? parseError.message : 'JSON Parse Error',
        raw_response: responseText
      }, { status: 400 });
    }

    if (!data) {
      await logEcocashCallback(
        payment.id,
        '400',
        'No data received from payment provider',
        { raw_response: responseText }
      );
      
      return NextResponse.json({
        status_code: '400',
        message: 'No data received from payment provider'
      }, { status: 400 });
    }

    console.log('Verification Response:', data);
    
    // Log the verification response
    await logEcocashCallback(
      payment.id,
      data.status_code || '500',
      data.message || 'No message',
      data
    );
    
    // Update payment status based on verification result
    let paymentStatus: EcocashPaymentStatus = EcocashPaymentStatus.PENDING;
    
    if (data.status_code === '200') {
      paymentStatus = EcocashPaymentStatus.COMPLETED;
    } else if (data.status_code === '401' || data.status_code === '400' || data.status_code === '415') {
      paymentStatus = EcocashPaymentStatus.FAILED;
    }
    
    // Update the payment record with verification results
    await updateEcocashPaymentVerification(
      payment.id,
      paymentStatus,
      data.status_code || '500',
      data.message || 'No message'
    );
    
    // Return the verification result with the payment ID
    return NextResponse.json({
      ...data,
      paymentId: payment.id,
      paymentStatus: paymentStatus
    }, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'Surrogate-Control': 'no-store'
      }
    });
    
  } catch (error) {
    console.error('EcoCash verification route error:', error);
    return NextResponse.json(
      { error: 'Failed to verify EcoCash transaction', status_code: '500' },
      { 
        status: 500,
        headers: {
          'Cache-Control': 'no-store, max-age=0',
          'Surrogate-Control': 'no-store'
        }
      }
    );
  }
}