import { NextResponse } from 'next/server';
import { initiateEcoCashPayment } from '@/lib/ecocash';
import { EcoCashPaymentRequest } from '@/lib/types';
import { Bearer, ECOCASH_MERCHANT_ID, ECOCASH_MERCHANT_NAME } from '@/lib/constants';
import { createEcocashPayment, logEcocashCallback, updateEcocashPaymentVerification } from '@/lib/db/ecocash';
import { EcocashPaymentStatus } from '@prisma/client';
import { prisma } from '@/lib/db';

export async function POST(request: Request) {
  
  try {
    // Check for authorization
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.includes(Bearer)) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();

    const paymentData: EcoCashPaymentRequest = {
      amount: body.amount,
      mobileNumber: body.mobileNumber,
      client_reference: body.client_reference,
      merchantid: ECOCASH_MERCHANT_ID,
      merchantname: ECOCASH_MERCHANT_NAME
    };

    // First record the payment attempt in the database
    const initialPayment = await createEcocashPayment(paymentData);

    // Then initiate the payment with Ecocash
    const response = await initiateEcoCashPayment(paymentData);

    // Get reference and status from response
    const { reference, status_code, message } = response;
    
    // Update the payment record with the response details
    if (initialPayment.id) {
      // Log the callback response
      await logEcocashCallback(
        initialPayment.id, 
        status_code || '500', 
        message || 'No response message', 
        response
      );
      
      // Update payment record with reference if successful
      if (status_code === '200' && reference) {
        // Instead of creating a new payment, update the existing one
        await updateEcocashPaymentVerification(
          initialPayment.id,
          EcocashPaymentStatus.PENDING,
          status_code,
          message || 'Payment initiated'
        );
        
        // Update the reference separately
        await prisma.ecocashPayment.update({
          where: { id: initialPayment.id },
          data: { reference }
        });
      } else if (status_code !== '200') {
        // Mark payment as failed if we got an error
        await updateEcocashPaymentVerification(
          initialPayment.id,
          EcocashPaymentStatus.FAILED,
          status_code || '500',
          message || 'Payment initiation failed'
        );
      }
    }
    
    // Add cache control headers
    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'Surrogate-Control': 'no-store'
      }
    });
    
  } catch (error) {
    console.error('EcoCash payment route error:', error);
    return NextResponse.json(
      { error: 'Failed to process payment' },
      { status: 500 }
    );
  }
}