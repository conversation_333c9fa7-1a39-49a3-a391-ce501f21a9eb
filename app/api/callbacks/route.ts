import { prisma } from "@/lib/db";
import { NextRequest, NextResponse } from "next/server";

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate the callback payload
    if (!body.reference) {
      return NextResponse.json(
        { error: 'Payment reference is required' },
        { status: 400 }
      );
    }
    
    // Find the payment record in database
    const payment = await prisma.mpesaPayment.findUnique({
      where: { reference: body.reference }
    });

    if (!payment) {
      return NextResponse.json(
        { error: 'Payment record not found' },
        { status: 404 }
      );
    }
    
    // Determine payment status from callback
    let paymentStatus: 'PENDING' | 'COMPLETED' | 'FAILED';
    
    if (body.status === 'success' || body.status_code === 'INS-0') {
      paymentStatus = 'COMPLETED';
    } else if (body.status === 'pending' || body.status_code === 'INS-666') {
      paymentStatus = 'PENDING';
    } else {
      paymentStatus = 'FAILED';
    }
    
    // Update payment record with callback data
    await prisma.mpesaPayment.update({
      where: { id: payment.id },
      data: {
        status: paymentStatus,
        statusMessage: body.message,
        transactionId: body.transaction_id,
        verificationReference: body.reference,
        verificationDate: new Date()
      }
    });
    
    // Store the callback data
    await prisma.paymentCallback.create({
      data: {
        paymentId: payment.id,
        statusCode: body.status_code || body.status || 'UNKNOWN',
        message: body.message || 'Callback received',
        rawPayload: body
      }
    });
    
    return NextResponse.json({
      success: true,
      message: 'Callback processed successfully'
    });
    
  } catch (error) {
    console.error('Payment callback error:', error);
    return NextResponse.json(
      { error: 'Failed to process payment callback' },
      { status: 500 }
    );
  }
}
