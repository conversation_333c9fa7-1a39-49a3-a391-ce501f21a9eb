import { NextResponse } from 'next/server';
import { Bear<PERSON> } from '@/lib/constants';
import ExternalPaymentService from '@/lib/services/external-payment-service';
import { withCors } from '@/lib/cors';

/**
 * API endpoint for checking payment status from external applications
 */
async function handler(request: Request) {
  console.log('Received payment status check request');
  
  try {
    // Check for authorization
    const authHeader = request.headers.get('authorization');
    if (!authHeader?.includes(Bearer)) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { 
          status: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Parse the request body
    const body = await request.json();
    const { sessionId } = body;
    
    if (!sessionId) {
      return NextResponse.json(
        { success: false, error: 'Session ID is required' },
        { 
          status: 400,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }
    
    // Find the session in the database
    const session = await ExternalPaymentService.findBySessionId(sessionId);
    
    if (!session) {
      return NextResponse.json(
        { success: false, error: 'Payment session not found' },
        { 
          status: 404,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }
    
    // Return a simplified version of the payment status
    let paymentStatus = 'pending';
    if (session.status === 'COMPLETED') {
      paymentStatus = 'completed';
    } else if (['FAILED', 'CANCELLED', 'EXPIRED'].includes(session.status)) {
      paymentStatus = 'failed';
    }
    
    return NextResponse.json({
      success: true,
      sessionId,
      status: paymentStatus,
      paymentMethod: session.selectedPaymentMethod || null,
      paymentReference: session.completedPaymentRef || null,
      transactionId: session.transactionId || null,
      completedAt: session.completedAt || null
    }, {
      headers: {
        'Cache-Control': 'no-store, max-age=0',
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization'
      }
    });
    
  } catch (error) {
    console.error('Payment status check error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check payment status' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      }
    );
  }
}

// Handle OPTIONS request for preflight
export async function OPTIONS(request: Request) {
  return NextResponse.json({}, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}

// Apply CORS middleware to POST handler
export const POST = withCors(handler);