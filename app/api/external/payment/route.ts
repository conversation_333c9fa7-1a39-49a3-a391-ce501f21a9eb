import { NextResponse } from 'next/server';
import { z } from 'zod';
import { Bearer } from '@/lib/constants';
import ExternalPaymentService from '@/lib/services/external-payment-service';
import { withCors } from '@/lib/cors';

// Define validation schema for external payment requests
const externalPaymentSchema = z.object({
  amount: z.string().min(1, "Amount is required"),
  reference: z.string().min(1, "Reference is required"),
  redirectUrl: z.string().url("Valid redirect URL is required").optional(),
  webhookUrl: z.string().url("Valid webhook URL is required").optional(),
  domain: z.string().optional(),
  appId: z.string().optional(),
  userId: z.string().optional(),
  metadata: z.record(z.unknown()).optional(),
});

export type ExternalPaymentRequest = z.infer<typeof externalPaymentSchema>;

/**
 * API endpoint for initiating payments from external applications
 */
async function handler(request: Request) {
  console.log('[API] Received external payment request');
  
  try {
    // Parse and validate the request body
    const body = await request.json();
    console.log('[API] Payment request body:', body);
    
    try {
      const validatedData = externalPaymentSchema.parse(body);
      console.log('[API] Validated payment data:', validatedData);
      
      // Use the client's reference as the session ID directly or generate a unique ID
      const sessionId = validatedData.reference;
      console.log('[API] Using reference as session ID:', sessionId);
      
      // Get the domain from the request if not provided in body
      const originDomain = validatedData.domain || request.headers.get('origin') || null;
      
      // Create default redirectUrl if not provided
      if (!validatedData.redirectUrl) {
        validatedData.redirectUrl = `${originDomain || process.env.NEXT_PUBLIC_EXTERNAL_PAYMENT_API}/payment-complete`;
      }
      
      // Store the payment request in the database with additional fields
      const session = await ExternalPaymentService.createSession(sessionId, {
        ...validatedData,
        originatingDomain: originDomain,
      });
      
      console.log('[API] Created payment session:', {
        id: session.id,
        sessionId: session.sessionId,
        status: session.status
      });
      
      // Create the URL for the payment UI
      const paymentUrl = new URL(`${process.env.NEXT_PUBLIC_EXTERNAL_PAYMENT_API}/payment/external/${sessionId}`);
      console.log('[API] Generated payment URL:', paymentUrl.toString());
      
      return NextResponse.json({
        success: true,
        sessionId,
        paymentUrl: paymentUrl.toString(),
      }, {
        headers: {
          'Cache-Control': 'no-store, max-age=0',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      });
      
    } catch (validationError) {
      console.error('[API] Validation error:', validationError);
      return NextResponse.json(
        { success: false, error: 'Invalid request data', details: "validationError - Please try again later" },
        { 
          status: 400,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }
    
  } catch (error) {
    console.error('[API] Payment processing error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to process payment request' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      }
    );
  }
}

// Handle OPTIONS request for preflight
export async function OPTIONS(request: Request) {
  return NextResponse.json({}, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}

// Apply CORS middleware to POST handler
export const POST = withCors(handler);