import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import ExternalPaymentService from '@/lib/services/external-payment-service';
import { withCors } from '@/lib/cors';

// Define validation schema for session update
const updateSessionSchema = z.object({
  paymentMethod: z.string().min(1, "Payment method is required"),
  paymentReference: z.string().min(1, "Payment reference is required"),
  transactionId: z.string().optional()
});

/**
 * API endpoint to update external payment session details
 */
async function handler(request: NextRequest, { params }: { params: { sessionId: string } }) {
  const sessionId = await params.sessionId;
  console.log('[API] Updating payment session:', sessionId);

  try {
    // Parse and validate request body
    const body = await request.json();
    console.log('[API] Update request body:', body);
    
    try {
      const validatedData = updateSessionSchema.parse(body);
      console.log('[API] Validated update data:', validatedData);

      // First, check if session exists
      const existingSession = await ExternalPaymentService.findBySessionId(sessionId);
      if (!existingSession) {
        console.log('[API] Session not found for update:', sessionId);
        return NextResponse.json(
          { success: false, error: 'Payment session not found' },
          { 
            status: 404,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'POST, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization'
            }
          }
        );
      }

      // Update session with payment details
      const updatedSession = await ExternalPaymentService.completeSession(
        sessionId,
        validatedData.paymentMethod,
        validatedData.paymentReference,
        validatedData.transactionId
      );

      console.log('[API] Session updated successfully:', {
        id: updatedSession.id,
        sessionId: updatedSession.sessionId,
        status: updatedSession.status
      });

      return NextResponse.json(
        { success: true, session: updatedSession },
        {
          headers: {
            'Cache-Control': 'no-store, max-age=0',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    } catch (validationError) {
      console.error('[API] Validation error:', validationError);
      return NextResponse.json(
        { success: false, error: 'Invalid update data', details: "validationError - Please try again later" },
        { 
          status: 400,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }
  } catch (error) {
    console.error('[API] Error updating session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to update payment session' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      }
    );
  }
}

// Handle OPTIONS request for preflight
export async function OPTIONS() {
  return NextResponse.json({}, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}

// Apply CORS middleware to POST handler
export const POST = withCors(handler);