import { NextRequest, NextResponse } from 'next/server';
import ExternalPaymentService from '@/lib/services/external-payment-service';
import { withCors } from '@/lib/cors';

/**
 * API endpoint to retrieve external payment session details
 */
async function handler(request: NextRequest, { params }: { params: { sessionId: string } }) {
  const { sessionId } = await params;
  console.log('[API] Fetching payment session:', sessionId);

  try {
    console.log('[API] Searching for session in database...');
    const session = await ExternalPaymentService.findBySessionId(sessionId);

    if (!session) {
      console.log('[API] Session not found:', sessionId);
      return NextResponse.json(
        { success: false, error: 'Payment session not found' },
        { 
          status: 404,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization'
          }
        }
      );
    }

    // Format response object
    const sessionResponse = {
      id: session.id,
      sessionId: session.sessionId,
      amount: session.amount,
      reference: session.reference,
      redirectUrl: session.redirectUrl,
      status: session.status,
      selectedPaymentMethod: session.selectedPaymentMethod,
      completedPaymentRef: session.completedPaymentRef,
      transactionId: session.transactionId,
      createdAt: session.createdAt,
      completedAt: session.completedAt
    };

    console.log('[API] Session found:', {
      id: session.id,
      sessionId: session.sessionId,
      status: session.status
    });

    return NextResponse.json(
      { success: true, session: sessionResponse },
      {
        headers: {
          'Cache-Control': 'no-store, max-age=0',
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      }
    );
  } catch (error) {
    console.error('[API] Error fetching session:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to retrieve payment session' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization'
        }
      }
    );
  }
}

// Handle OPTIONS request for preflight
export async function OPTIONS() {
  return NextResponse.json({}, {
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  });
}

// Apply CORS middleware to GET handler
export const GET = withCors(handler);