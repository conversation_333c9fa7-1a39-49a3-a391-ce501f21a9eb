"use server";

import { paymentMethodsAvailabiltyBearerToken } from "@/lib/constants";
import {
  checkPaymentMethodAvailability,
  upsertPaymentMethodAvailability,
} from "@/lib/paymentMethodsAvailabilty";

function extractPaymentMethodFromUrl(request: Request): string | null {
  try {
    // Get the URL from the request
    const url = new URL(request.url);
    
    // Extract the paymentMethod query parameter
    const paymentMethod = url.searchParams.get('paymentMethod');
    
    return paymentMethod;
  } catch (error) {
    console.error('Error extracting payment method from URL:', error);
    return null;
  }
}

export async function POST(request: Request) {
  try {
    const bearerToken = request.headers.get("Authorization")?.split(" ")[1];
    if (!bearerToken || bearerToken !== paymentMethodsAvailabiltyBearerToken) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
      });
    }

    const body = await request.json();
    console.log("body: ", body);
    const { paymentMethod, isAvailable, id } = body;

    if (!paymentMethod) {
      return new Response(
        JSON.stringify({ error: "Payment method is required" }),
        { status: 400 }
      );
    }

    console.log("id: ", id)

    if (id || id.length > 0) {
      const availability = await upsertPaymentMethodAvailability(
        paymentMethod,
        isAvailable,
        id
      );
      return new Response(JSON.stringify(availability));
    }

    const availability = await upsertPaymentMethodAvailability(
      paymentMethod,
      isAvailable
    );
    return new Response(JSON.stringify(availability));
  } catch (error) {
    console.error("Payment method availability create/update error:", error);
    return new Response(
      JSON.stringify({
        error: "Failed to create/update payment method availability",
      }),
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const bearerToken = request.headers.get("Authorization")?.split(" ")[1];
    if (!bearerToken || bearerToken.toString() !== paymentMethodsAvailabiltyBearerToken) {
      return new Response(JSON.stringify({ error: "Unauthorized" }), {
        status: 401,
      });
    }

    const paymentMethod = extractPaymentMethodFromUrl(request);

    if (!paymentMethod) {
      return new Response(
        JSON.stringify({ error: "Payment method is required" }),
        { status: 400 }
      );
    }

    const availability = await checkPaymentMethodAvailability(paymentMethod);
    return new Response(JSON.stringify(availability));
  } catch (error) {
    console.error("Payment method availability check error:", error);
    return new Response(
      JSON.stringify({ error: "Failed to check payment method availability" }),
      { status: 500 }
    );
  }
}
